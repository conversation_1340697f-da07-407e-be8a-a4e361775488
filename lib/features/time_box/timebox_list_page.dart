import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../services/floating_timer_service.dart';
import '../../services/global_timer_service.dart';
import '../vocabulary/vocabulary_service.dart';
import '../exercise/custom_action_library_service.dart';
import '../exercise/pao_integration_service.dart';
import 'models/timebox_models.dart';
import 'providers/timebox_provider.dart';
import 'managers/task_category_manager.dart';
import 'pages/task_category_management_page.dart';
import 'package:flutter/scheduler.dart';
import 'dart:async';
import 'dart:math';
import '../study_time/providers/study_time_providers.dart';

/// 任务分类颜色工具类（向后兼容）
///
/// 这个类保留用于向后兼容，新的颜色管理应该使用 TaskCategoryManager
class SubjectColors {
  /// 根据分类获取对应颜色
  static Color getCategoryColor(String category) {
    // 首先尝试从 TaskCategoryManager 获取颜色
    try {
      return taskCategoryManager.getCategoryColor(category);
    } catch (e) {
      // 如果失败，回退到硬编码颜色
      switch (category) {
        case '计算机科学':
          return const Color(0xFFE03E3E); // 红色
        case '数学':
          return const Color(0xFFFFD700); // 荧光黄
        case '英语':
          return const Color(0xFF0F7B6C); // 绿色
        case '政治':
          return const Color(0xFF2E7EED); // 蓝色
        case '休息':
          return const Color(0xFF0F7B6C); // 绿色（具身记忆用绿色）
        default:
          return const Color(0xFF9B9A97); // 默认灰色
      }
    }
  }

  /// 根据分类获取带透明度的颜色
  static Color getCategoryColorWithOpacity(String category, double opacity) {
    final color = getCategoryColor(category);
    return color.withValues(alpha: opacity);
  }
}

/// 时间盒子列表页面
///
/// 这是OneDay应用的核心任务管理界面，提供以下功能：
/// - 时间盒子列表展示和管理
/// - 拖拽重排序和滑动操作
/// - 任务创建和编辑
/// - 状态管理和筛选功能
/// - 工资计算和统计展示
class TimeBoxListPage extends ConsumerStatefulWidget {
  const TimeBoxListPage({super.key});

  @override
  ConsumerState<TimeBoxListPage> createState() => _TimeBoxListPageState();
}

class _TimeBoxListPageState extends ConsumerState<TimeBoxListPage>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  // 筛选状态
  TaskStatus? _selectedStatus;
  TaskPriority? _selectedPriority;
  String? _selectedCategory;
  bool _showAllTasks = true; // 是否显示所有任务（默认显示所有任务以避免过滤问题）

  /// 根据分类获取对应颜色
  Color _getCategoryColor(String category) {
    return SubjectColors.getCategoryColor(category);
  }

  // 高精度计时器状态（已弃用，使用全局计时器服务）
  Ticker? _ticker;

  // 小窗口状态
  bool _isFloatingWindowVisible = false;
  Offset _floatingWindowPosition = const Offset(20, 100);

  // 动画控制器
  late AnimationController _floatingWindowAnimationController;
  late Animation<double> _floatingWindowAnimation;

  // 全局计时器服务
  final GlobalTimerService _globalTimerService = GlobalTimerService();

  // 浮动计时器服务
  final FloatingTimerService _floatingTimerService = FloatingTimerService();

  // 防止重复显示动觉记忆法页面
  bool _isKinestheticLearningShowing = false;

  // 过滤后的任务列表
  List<TimeBoxTask> get _filteredTasks {
    final timeBoxState = ref.watch(timeBoxProvider);
    final today = DateTime.now();

    print(
      '🔍 开始过滤任务 - 总任务数: ${timeBoxState.tasks.length}, 显示模式: ${_showAllTasks ? "全部任务" : "今日任务"}',
    );

    var filtered = timeBoxState.tasks.where((task) {
      // 如果不显示所有任务，则只显示今日相关的任务
      if (!_showAllTasks) {
        final todayStart = DateTime(today.year, today.month, today.day);
        final todayEnd = todayStart.add(const Duration(days: 1));

        // 更宽松的今日任务判断：
        // 1. 创建时间在今天
        // 2. 开始时间在今天
        // 3. 结束时间在今天
        // 4. 任务跨越今天（开始时间在今天之前，结束时间在今天之后或为空）
        final createdToday =
            task.createdAt.isAfter(
              todayStart.subtract(const Duration(milliseconds: 1)),
            ) &&
            task.createdAt.isBefore(todayEnd);

        final startedToday =
            task.startTime != null &&
            task.startTime!.isAfter(
              todayStart.subtract(const Duration(milliseconds: 1)),
            ) &&
            task.startTime!.isBefore(todayEnd);

        final endedToday =
            task.endTime != null &&
            task.endTime!.isAfter(
              todayStart.subtract(const Duration(milliseconds: 1)),
            ) &&
            task.endTime!.isBefore(todayEnd);

        final spansToday =
            task.startTime != null &&
            task.startTime!.isBefore(todayStart) &&
            (task.endTime == null || task.endTime!.isAfter(todayStart));

        final isToday =
            createdToday || startedToday || endedToday || spansToday;

        print('📅 任务 "${task.title}" (ID: ${task.id}) 日期检查:');
        print('   创建时间: ${task.createdAt} (今日: $createdToday)');
        print('   开始时间: ${task.startTime} (今日: $startedToday)');
        print('   结束时间: ${task.endTime} (今日: $endedToday)');
        print('   跨越今日: $spansToday');
        print('   今日范围: $todayStart - $todayEnd');
        print('   是否今日任务: $isToday');

        if (!isToday) {
          print('   ❌ 被日期过滤排除');
          return false;
        }
      }

      // 状态过滤
      if (_selectedStatus != null && task.status != _selectedStatus) {
        print('   ❌ 被状态过滤排除: ${task.status} != $_selectedStatus');
        return false;
      }

      // 优先级过滤
      if (_selectedPriority != null && task.priority != _selectedPriority) {
        print('   ❌ 被优先级过滤排除: ${task.priority} != $_selectedPriority');
        return false;
      }

      // 分类过滤
      if (_selectedCategory != null && task.category != _selectedCategory) {
        print('   ❌ 被分类过滤排除: ${task.category} != $_selectedCategory');
        return false;
      }

      print('   ✅ 任务通过所有过滤条件');
      return true;
    }).toList();

    // 默认按创建时间倒序排列（最新的在前）
    filtered.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    print('📊 过滤结果: ${filtered.length} 个任务通过过滤');
    for (final task in filtered) {
      print(
        '   ✅ "${task.title}" (ID: ${task.id}, 状态: ${task.status.displayName})',
      );
    }

    return filtered;
  }

  // 当前显示任务的统计数据
  Map<String, dynamic> get _currentStats {
    // 使用当前过滤后的任务列表进行统计
    final currentTasks = _filteredTasks;

    final completedTasks = currentTasks
        .where((task) => task.status == TaskStatus.completed)
        .length;
    final totalPlannedMinutes = currentTasks.fold<int>(
      0,
      (sum, task) => sum + task.plannedMinutes,
    );
    // 只计算已完成任务的实际工资
    final totalEarnedWage = currentTasks
        .where((task) => task.status == TaskStatus.completed)
        .fold<double>(0, (sum, task) => sum + task.calculateWage());

    return {
      'totalTasks': currentTasks.length,
      'completedTasks': completedTasks,
      'totalMinutes': totalPlannedMinutes,
      'totalWage': totalEarnedWage,
    };
  }

  // 安全地dispose Ticker
  void _safeDisposeTicker() {
    try {
      _ticker?.dispose();
      _ticker = null;
    } catch (e) {
      print('Ticker dispose error: $e');
    }
  }

  /// 全局计时器状态变化回调
  void _onGlobalTimerStateChanged() {
    // 双重检查：确保widget仍然mounted且未被dispose
    if (!mounted) {
      print('⚠️ Widget已dispose，跳过状态更新');
      return;
    }

    try {
      setState(() {
        // 触发UI重建以反映全局计时器状态
      });

      // 检查是否刚进入休息时间，如果是则自动显示动觉记忆法页面
      if (_globalTimerService.pomodoroState == PomodoroTimerState.rest &&
          _globalTimerService.isTimerRunning &&
          !_isKinestheticLearningShowing) {
        // 延迟到下一帧执行导航操作，避免在setState期间导航
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && !_isKinestheticLearningShowing) {
            _showKinestheticLearningInterface();
          }
        });
      }
    } catch (e) {
      print('⚠️ setState错误: $e');
    }
  }

  /// 休息完成回调
  void _onRestCompleted() {
    if (mounted) {
      print('🏁 休息时间完成，显示任务完成对话框');
      _showTaskCompletionDialog();
    }
  }

  /// 导航到活跃的计时器页面
  void _navigateToActiveTimer() {
    if (_globalTimerService.hasActiveTimer) {
      print('🎯 导航到活跃计时器页面');
      // 这里可以添加导航到具体计时器页面的逻辑
      // 或者直接在当前页面显示计时器UI
      setState(() {
        // 显示内联计时器
      });
    }
  }

  /// 更新任务列表中的任务
  void _updateTaskInList(TimeBoxTask updatedTask) {
    // 使用非阻塞的方式更新任务，避免阻塞计时器启动
    ref
        .read(timeBoxProvider.notifier)
        .updateTask(updatedTask)
        .then((_) {
          if (mounted) {
            setState(() {
              // 触发UI重建以反映任务更新
            });
            print('✅ 任务更新成功: ${updatedTask.title}');

            // 输出更新后的统计信息
            final stats = _currentStats;
            print(
              '📊 更新后统计 - 总任务: ${stats['totalTasks']}, 已完成: ${stats['completedTasks']}',
            );
          }
        })
        .catchError((e) {
          print('❌ 更新任务列表失败: $e');
          // 显示错误提示但不阻塞主流程
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('更新任务失败: $e'),
                backgroundColor: Colors.orange,
                duration: const Duration(seconds: 2),
              ),
            );
          }
        });
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // 初始化动画控制器
    _floatingWindowAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _floatingWindowAnimation = CurvedAnimation(
      parent: _floatingWindowAnimationController,
      curve: Curves.easeInOut,
    );

    // 设置全局计时器服务的导航回调
    _globalTimerService.setNavigationCallback(() {
      _navigateToActiveTimer();
    });

    // 设置任务更新回调
    _globalTimerService.setTaskUpdateCallback((updatedTask) {
      _updateTaskInList(updatedTask);
    });

    // 设置休息完成回调
    _globalTimerService.setRestCompletedCallback(() {
      _onRestCompleted();
    });

    // 监听全局计时器状态变化
    _globalTimerService.addListener(_onGlobalTimerStateChanged);

    // 检查是否有活跃的计时器需要恢复显示，并确保数据一致性
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _globalTimerService.checkAndRestoreTimerState();
      _ensureDataConsistency();
    });
  }

  /// 确保数据一致性（静默执行）
  void _ensureDataConsistency() {
    // 静默刷新数据以确保一致性，不显示任何UI反馈
    ref
        .read(timeBoxProvider.notifier)
        .refresh()
        .then((_) {
          if (mounted) {
            setState(() {
              // 触发UI重建以反映最新数据
            });
            print('📊 时间盒子数据一致性检查完成');

            // 输出调试信息
            final timeBoxState = ref.read(timeBoxProvider);
            final filteredCount = _filteredTasks.length;
            final totalCount = timeBoxState.tasks.length;
            print('📊 数据统计 - 总任务数: $totalCount, 过滤后任务数: $filteredCount');
            print('📊 显示模式: ${_showAllTasks ? "全部任务" : "今日任务"}');
          }
        })
        .catchError((e) {
          // 静默处理错误，只记录日志
          print('❌ 数据一致性检查失败: $e');
        });
  }

  /// 清除工资统计数据（Debug功能）
  void _clearWageStatistics() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清除工资统计数据'),
        content: const Text(
          '这将清除所有缓存的学习时间统计和工资数据，应用将重新计算。\n\n'
          '这个操作用于修复工资计算错误问题。确定要继续吗？',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('确定', style: TextStyle(color: Color(0xFFE03E3E))),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // 导入学习时间统计服务
        final studyTimeService = ref.read(studyTimeStatisticsServiceProvider);

        // 清除统计数据
        await studyTimeService.clearAllStatistics();

        // 刷新时间盒子数据
        await ref.read(timeBoxProvider.notifier).refresh();

        // 重新计算统计
        _ensureDataConsistency();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('工资统计数据已清除，正在重新计算...'),
              backgroundColor: Color(0xFF0F7B6C),
              duration: Duration(seconds: 2),
            ),
          );
        }
      } catch (e) {
        print('❌ 清除工资统计数据失败: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('清除失败: $e'),
              backgroundColor: const Color(0xFFE03E3E),
            ),
          );
        }
      }
    }
  }

  @override
  void dispose() {
    _safeDisposeTicker();
    _floatingWindowAnimationController.dispose();
    _globalTimerService.removeListener(_onGlobalTimerStateChanged);
    WidgetsBinding.instance.removeObserver(this);
    // 注意：不要在dispose中关闭浮动窗口，因为它应该在系统级别持续显示
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // 处理应用生命周期变化
    switch (state) {
      case AppLifecycleState.resumed:
        // 应用回到前台，检查是否需要恢复计时器状态
        print('▶️ 应用回到前台，检查计时器状态');
        _globalTimerService.checkAndRestoreTimerState();
        break;
      default:
        break;
    }
  }

  // 计时器控制方法（使用全局计时器服务）
  void _startTimer(TimeBoxTask task) async {
    print('🚀 启动计时器: ${task.title}');

    try {
      // 使用全局计时器服务启动计时器
      await _globalTimerService.startTimer(task);

      // 更新本地状态以显示UI
      if (mounted) {
        setState(() {
          // UI状态会通过全局计时器服务的监听器自动更新
        });
      }

      print('✅ 计时器启动成功: ${task.title}');
    } catch (e, stackTrace) {
      print('❌ 启动计时器失败: $e');
      print('Stack trace: $stackTrace');

      // 显示错误提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('启动计时器失败: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _pauseTimer() {
    _globalTimerService.pauseTimer();
  }

  void _resumeTimer() {
    _globalTimerService.resumeTimer();
  }

  void _stopTimer() {
    _globalTimerService.stopTimer();
  }

  // 切换小窗口显示状态
  Future<void> _toggleFloatingWindow() async {
    if (_globalTimerService.currentTask == null) return;

    if (_floatingTimerService.isVisible) {
      // 隐藏系统级浮动窗口，显示页面内窗口
      _floatingTimerService.hideFloatingTimer();
      setState(() {
        _isFloatingWindowVisible = true;
        // 智能定位到当前任务附近
        _positionFloatingWindowNearTask();
      });
      // 启动显示动画
      _floatingWindowAnimationController.forward();
    } else {
      if (_isFloatingWindowVisible) {
        // 隐藏页面内窗口，显示系统级浮动窗口
        await _floatingWindowAnimationController.reverse();
        setState(() {
          _isFloatingWindowVisible = false;
        });
      }
      if (mounted) {
        await _globalTimerService.showFloatingTimer(context);
      }
    }
    HapticFeedback.lightImpact();
  }

  // 智能定位浮动窗口到当前任务附近
  void _positionFloatingWindowNearTask() {
    final currentTask = _globalTimerService.currentTask;
    if (currentTask == null) return;

    // 查找当前任务在列表中的位置
    final timeBoxState = ref.read(timeBoxProvider);
    final taskIndex = timeBoxState.tasks.indexWhere(
      (task) => task.id == currentTask.id,
    );
    if (taskIndex == -1) return;

    // 计算任务卡片的大概位置
    final screenSize = MediaQuery.of(context).size;
    final appBarHeight = kToolbarHeight + MediaQuery.of(context).padding.top;
    final taskCardHeight = 80.0; // 估算的任务卡片高度
    final listPadding = 16.0;

    // 计算任务在屏幕上的大概Y位置
    double taskY =
        appBarHeight + listPadding + (taskIndex * (taskCardHeight + 12));

    // 将浮动窗口定位到任务右侧
    double windowX = screenSize.width - 180; // 距离右边缘20px
    double windowY = taskY - 10; // 稍微向上偏移

    // 确保窗口在屏幕范围内
    windowX = windowX.clamp(20, screenSize.width - 180);
    windowY = windowY.clamp(appBarHeight + 20, screenSize.height - 120);

    _floatingWindowPosition = Offset(windowX, windowY);
  }

  // 关闭小窗口
  void _closeFloatingWindow() async {
    // 播放隐藏动画
    await _floatingWindowAnimationController.reverse();
    setState(() {
      _isFloatingWindowVisible = false;
    });
    // 同时关闭系统级浮动窗口
    _floatingTimerService.hideFloatingTimer();
  }

  // 更新小窗口位置
  void _updateFloatingWindowPosition(Offset newPosition) {
    setState(() {
      _floatingWindowPosition = newPosition;
    });
  }

  void _showTaskCompletionDialog() {
    // 优先使用当前任务，如果没有则使用最后完成的任务
    final task =
        _globalTimerService.currentTask ??
        _globalTimerService.lastCompletedTask;
    if (task == null) {
      print('⚠️ 没有找到要显示的任务信息');
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🎉 任务完成'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('任务："${task.title}"'),
            const SizedBox(height: 8),
            Text('学习时长：${task.plannedMinutes} 分钟'),
            Text('获得工资：¥${task.calculateWage().toStringAsFixed(1)}'),
            const SizedBox(height: 12),
            const Text(
              '恭喜完成学习任务！继续保持专注学习的好习惯。',
              style: TextStyle(fontSize: 14, color: Color(0xFF787774)),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // 不需要再次调用 _stopTimer()，因为计时器已经停止
            },
            child: const Text('完成'),
          ),
        ],
      ),
    );
  }

  void _showKinestheticLearningInterface() {
    // 避免重复显示
    if (_isKinestheticLearningShowing) {
      print('⚠️ 动觉记忆法界面已在显示中，跳过重复显示');
      return;
    }

    // 检查widget是否仍然mounted
    if (!mounted) {
      print('⚠️ Widget已dispose，无法显示动觉记忆法界面');
      return;
    }

    print('🧘 开始显示动觉记忆法界面');
    _isKinestheticLearningShowing = true;

    try {
      context
          .push(
            '/kinesthetic-learning',
            extra: {
              'onComplete': () {
                print('🏁 动觉记忆法界面完成回调');
                _isKinestheticLearningShowing = false;
                context.pop();
                // 休息完成后不需要显示任务完成对话框，因为会通过全局计时器服务的回调处理
              },
              'onSkipRest': () {
                print('⏭️ 用户选择跳过休息时间');
                _isKinestheticLearningShowing = false;
                // 提前结束休息时间，触发休息完成回调
                _globalTimerService.skipRestTime();
                // 立即关闭动觉记忆法页面
                context.pop();
              },
            },
          )
          .then((_) {
            // 确保页面关闭后重置状态
            print('🔄 动觉记忆法界面已关闭，重置状态');
            _isKinestheticLearningShowing = false;
          })
          .catchError((error) {
            print('❌ 动觉记忆法界面导航错误: $error');
            _isKinestheticLearningShowing = false;
          });
    } catch (e) {
      print('❌ 显示动觉记忆法界面时发生错误: $e');
      _isKinestheticLearningShowing = false;
    }
  }

  // Note: _formatTime method removed as it was unused
  // String _formatTime(int seconds) {
  //   final minutes = seconds ~/ 60;
  //   final secs = seconds % 60;
  //   return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  // }

  Widget _buildInlineTimer() {
    final currentTask = _globalTimerService.currentTask;
    if (currentTask == null) return const SizedBox.shrink();

    final remainingSeconds = _globalTimerService.remainingSeconds;
    final totalSeconds = _globalTimerService.totalDurationSeconds > 0
        ? _globalTimerService.totalDurationSeconds
        : currentTask.plannedMinutes * 60;
    final progress = (1.0 - (remainingSeconds / totalSeconds)).clamp(0.0, 1.0);
    final elapsedMinutes =
        (currentTask.plannedMinutes * 60 - remainingSeconds) / 60.0;
    final currentWage = (elapsedMinutes / 60.0) * 200.0;

    return Container(
      margin: const EdgeInsets.only(left: 16, right: 16, top: 0, bottom: 4), // 完全移除顶部边距，实现相切
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _isFloatingWindowVisible
              ? const Color(0xFF2E7EED).withValues(alpha: 0.6)
              : const Color(0xFF2E7EED).withValues(alpha: 0.3),
          width: _isFloatingWindowVisible ? 2 : 1,
        ),
        boxShadow: _isFloatingWindowVisible
            ? [
                BoxShadow(
                  color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
                  blurRadius: 8,
                  spreadRadius: 2,
                ),
              ]
            : null,
      ),
      child: Column(
        children: [
          // 进度条（顶部）
          Container(
            width: double.infinity,
            height: 3,
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(8),
              ),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: progress,
              child: Container(
                decoration: BoxDecoration(
                  color: const Color(0xFF2E7EED),
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(8),
                  ),
                ),
              ),
            ),
          ),

          // 主要内容
          Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                // 状态指示器
                Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    color: _globalTimerService.isTimerRunning
                        ? const Color(0xFF0F7B6C)
                        : const Color(0xFF9B9A97),
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),

                // 时间显示
                Text(
                  _globalTimerService.formatTime(
                    _globalTimerService.remainingSeconds,
                  ),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF37352F),
                    fontFamily: 'monospace',
                  ),
                ),

                const SizedBox(width: 12),

                // 状态文字
                Text(
                  _globalTimerService.isTimerRunning ? '进行中' : '已暂停',
                  style: TextStyle(
                    fontSize: 12,
                    color: _globalTimerService.isTimerRunning
                        ? const Color(0xFF0F7B6C)
                        : const Color(0xFF9B9A97),
                    fontWeight: FontWeight.w500,
                  ),
                ),

                const Spacer(),

                // 工资显示
                Text(
                  '¥${currentWage.toStringAsFixed(1)}',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF0F7B6C),
                  ),
                ),

                const SizedBox(width: 12),

                // 控制按钮
                Row(
                  children: [
                    _buildCompactTimerButton(
                      icon: _globalTimerService.isTimerRunning
                          ? Icons.pause
                          : Icons.play_arrow,
                      onPressed: _globalTimerService.isTimerRunning
                          ? _pauseTimer
                          : _resumeTimer,
                      color: const Color(0xFF2E7EED),
                    ),
                    const SizedBox(width: 6),
                    _buildCompactTimerButton(
                      icon: Icons.stop,
                      onPressed: _stopTimer,
                      color: const Color(0xFFE03E3E),
                    ),
                    const SizedBox(width: 6),
                    _buildCompactTimerButton(
                      icon: _isFloatingWindowVisible
                          ? Icons.picture_in_picture
                          : Icons.picture_in_picture_alt,
                      onPressed: _toggleFloatingWindow,
                      color: _isFloatingWindowVisible
                          ? const Color(0xFF2E7EED)
                          : const Color(0xFF9B9A97),
                      tooltip: _isFloatingWindowVisible ? '隐藏小窗口' : '小窗口显示',
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactTimerButton({
    required IconData icon,
    required VoidCallback onPressed,
    required Color color,
    String? tooltip,
  }) {
    Widget button = GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Icon(icon, color: Colors.white, size: 16),
      ),
    );

    if (tooltip != null) {
      return Tooltip(message: tooltip, child: button);
    }

    return button;
  }

  // 构建浮动计时器小窗口
  Widget _buildFloatingTimerWindow() {
    final currentTask = _globalTimerService.currentTask;
    if (currentTask == null) return const SizedBox.shrink();

    final remainingSeconds = _globalTimerService.remainingSeconds;
    final totalSeconds = _globalTimerService.totalDurationSeconds > 0
        ? _globalTimerService.totalDurationSeconds
        : currentTask.plannedMinutes * 60;
    final progress = (1.0 - (remainingSeconds / totalSeconds)).clamp(0.0, 1.0);

    return Positioned(
      left: _floatingWindowPosition.dx,
      top: _floatingWindowPosition.dy,
      child: AnimatedBuilder(
        animation: _floatingWindowAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _floatingWindowAnimation.value,
            child: Opacity(
              opacity: _floatingWindowAnimation.value,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 连接线指示器
                  _buildConnectionIndicator(),

                  // 浮动窗口主体
                  GestureDetector(
                    onPanUpdate: (details) {
                      final screenSize = MediaQuery.of(context).size;
                      final windowWidth = 160.0;
                      final windowHeight = 80.0; // 增加高度以容纳进度条

                      // 计算新位置，确保窗口不超出屏幕边界
                      double newX =
                          (_floatingWindowPosition.dx + details.delta.dx).clamp(
                            0,
                            screenSize.width - windowWidth,
                          );
                      double newY =
                          (_floatingWindowPosition.dy + details.delta.dy).clamp(
                            0,
                            screenSize.height - windowHeight - 100,
                          ); // 减去底部安全区域

                      _updateFloatingWindowPosition(Offset(newX, newY));
                    },
                    child: Material(
                      elevation: 6,
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.white,
                      child: Container(
                        width: 160,
                        height: 80,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: const Color(
                              0xFF2E7EED,
                            ).withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          children: [
                            // 进度条（顶部）- 与内联计时器保持一致
                            Container(
                              width: double.infinity,
                              height: 3,
                              decoration: BoxDecoration(
                                color: Colors.grey.shade100,
                                borderRadius: const BorderRadius.vertical(
                                  top: Radius.circular(8),
                                ),
                              ),
                              child: FractionallySizedBox(
                                alignment: Alignment.centerLeft,
                                widthFactor: progress,
                                child: Container(
                                  decoration: const BoxDecoration(
                                    color: Color(0xFF2E7EED),
                                    borderRadius: BorderRadius.vertical(
                                      top: Radius.circular(8),
                                    ),
                                  ),
                                ),
                              ),
                            ),

                            // 主要内容 - 与内联计时器样式保持一致
                            Expanded(
                              child: Padding(
                                padding: const EdgeInsets.all(8),
                                child: Row(
                                  children: [
                                    // 状态指示器
                                    Container(
                                      width: 4,
                                      height: 4,
                                      decoration: BoxDecoration(
                                        color:
                                            _globalTimerService.isTimerRunning
                                            ? const Color(0xFF0F7B6C)
                                            : const Color(0xFF9B9A97),
                                        shape: BoxShape.circle,
                                      ),
                                    ),
                                    const SizedBox(width: 6),

                                    // 时间显示
                                    Expanded(
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          // 任务标题
                                          Text(
                                            _globalTimerService.pomodoroState ==
                                                    PomodoroTimerState.rest
                                                ? '休息中'
                                                : currentTask.title,
                                            style: const TextStyle(
                                              fontSize: 9,
                                              fontWeight: FontWeight.w600,
                                              color: Color(0xFF37352F),
                                            ),
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          const SizedBox(height: 2),
                                          // 剩余时间
                                          Text(
                                            _globalTimerService.formatTime(
                                              _globalTimerService
                                                  .remainingSeconds,
                                            ),
                                            style: const TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w700,
                                              color: Color(0xFF37352F),
                                              fontFamily: 'monospace',
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),

                                    // 关闭按钮
                                    GestureDetector(
                                      onTap: _closeFloatingWindow,
                                      child: Container(
                                        width: 16,
                                        height: 16,
                                        decoration: BoxDecoration(
                                          color: Colors.grey.shade400,
                                          shape: BoxShape.circle,
                                        ),
                                        child: const Icon(
                                          Icons.close,
                                          size: 10,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // 构建连接指示器
  Widget _buildConnectionIndicator() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 连接点
        Container(
          width: 6,
          height: 6,
          decoration: BoxDecoration(
            color: const Color(0xFF2E7EED),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF2E7EED).withValues(alpha: 0.3),
                blurRadius: 4,
                spreadRadius: 1,
              ),
            ],
          ),
        ),
        // 连接线
        Container(
          width: 2,
          height: 12,
          decoration: BoxDecoration(
            color: const Color(0xFF2E7EED).withValues(alpha: 0.6),
            borderRadius: BorderRadius.circular(1),
          ),
        ),
      ],
    );
  }

  // 【开发者测试区块】动觉记忆法快速入口 - 此区块可以整体删除
  Widget _buildDeveloperTestSection() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFFFE4B5), // 淡橙色背景，区分于正常功能
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFDEB887), width: 1),
      ),
      child: Row(
        children: [
          // 开发者图标
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: const Color(0xFFFF8C00),
              borderRadius: BorderRadius.circular(6),
            ),
            child: const Icon(
              Icons.developer_mode,
              color: Colors.white,
              size: 16,
            ),
          ),

          const SizedBox(width: 12),

          // 文字说明
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '开发者测试',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF8B4513),
                  ),
                ),
                Text(
                  '动觉记忆法界面快速入口',
                  style: TextStyle(fontSize: 12, color: Color(0xFF8B4513)),
                ),
              ],
            ),
          ),

          // 快速进入按钮
          GestureDetector(
            onTap: _navigateToKinestheticDeveloperTest,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFFFF8C00),
                borderRadius: BorderRadius.circular(6),
              ),
              child: const Text(
                '进入',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 【开发者测试】直接跳转动觉记忆法界面的方法
  void _navigateToKinestheticDeveloperTest() {
    context.push(
      '/kinesthetic-learning',
      extra: {
        'onComplete': () {
          context.pop();
          // 开发者测试模式，直接返回，不显示任务完成对话框
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('开发者测试完成'),
              duration: Duration(seconds: 1),
            ),
          );
        },
      },
    );
  }

  // 【开发者测试】日历时间块测试入口构建
  Widget _buildCalendarTestSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFE6F3FF), // 淡蓝色背景
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFF2E7EED), width: 1),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: const Color(0xFF2E7EED),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.calendar_today,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '日历时间块测试',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF2E7EED),
                  ),
                ),
                Text(
                  '创建3个测试任务：30分钟(半格)、15分钟(1/4格)、60分钟(全格)',
                  style: TextStyle(fontSize: 11, color: Color(0xFF2E7EED)),
                ),
              ],
            ),
          ),
          
          // 快速创建测试任务按钮
          GestureDetector(
            onTap: _createCalendarTestTask,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFF2E7EED),
                borderRadius: BorderRadius.circular(6),
              ),
              child: const Text(
                '测试',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 【开发者测试】创建日历测试任务
  void _createCalendarTestTask() async {
    final now = DateTime.now();
    
    // 创建一个明显的测试任务：今天14:00-14:30（30分钟，应该显示为半格高度）
    final testStartTime = DateTime(now.year, now.month, now.day, 14, 0); // 今天14:00开始
    final testEndTime = DateTime(now.year, now.month, now.day, 14, 30); // 今天14:30结束
    
    // 创建一个30分钟的已完成测试任务
    final testTask = TimeBoxTask(
      id: 'calendar_test_${now.millisecondsSinceEpoch}',
      title: '📊 日历时间块测试',
      description: '30分钟测试任务，验证时间块显示功能。应显示为半格高度。',
      plannedMinutes: 30, // 30分钟，应该显示为半格高度(40px out of 80px)
      status: TaskStatus.completed, // 标记为已完成
      priority: TaskPriority.high, // 使用高优先级，颜色更明显
      category: '计算机科学', // 使用红色分类，更明显
      createdAt: now.subtract(const Duration(hours: 1)),
      startTime: testStartTime,
      endTime: testEndTime,
    );
    
    try {
      // 添加测试任务到状态
      await ref.read(timeBoxProvider.notifier).addTask(testTask);
      
      // 创建额外的测试任务来验证不同时长的显示效果
      await _createAdditionalTestTasks();
      
      // 显示成功提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '✅ 测试任务已创建！\n'
              '📅 请查看今天日历的14:00时段\n'
              '⏱️ 30分钟任务应显示为半格高度',
            ),
            backgroundColor: const Color(0xFF0F7B6C),
            duration: const Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
      
      // 延迟2秒后自动跳转到日历页面
      await Future.delayed(const Duration(seconds: 2));
      if (mounted) {
        context.go('/calendar');
      }
    } catch (e) {
      // 显示错误提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ 创建测试任务失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // 【开发者测试】创建额外的测试任务来验证不同时长
  Future<void> _createAdditionalTestTasks() async {
    final now = DateTime.now();
    
    // 创建一个15分钟任务（四分之一格高度）
    final task15min = TimeBoxTask(
      id: 'test_15min_${now.millisecondsSinceEpoch}',
      title: '📋 15分钟测试',
      description: '15分钟测试任务，应显示为1/4格高度',
      plannedMinutes: 15, // 15分钟，应显示为1/4格高度
      status: TaskStatus.completed,
      priority: TaskPriority.medium,
      category: '英语',
      createdAt: now.subtract(const Duration(hours: 1)),
      startTime: DateTime(now.year, now.month, now.day, 15, 0), // 15:00开始
      endTime: DateTime(now.year, now.month, now.day, 15, 15), // 15:15结束
    );
    
    // 创建一个60分钟任务（完整格高度）
    final task60min = TimeBoxTask(
      id: 'test_60min_${now.millisecondsSinceEpoch}',
      title: '📚 60分钟测试',
      description: '60分钟测试任务，应显示为完整格高度',
      plannedMinutes: 60, // 60分钟，应显示为完整格高度
      status: TaskStatus.completed,
      priority: TaskPriority.high,
      category: '数学',
      createdAt: now.subtract(const Duration(hours: 1)),
      startTime: DateTime(now.year, now.month, now.day, 16, 0), // 16:00开始
      endTime: DateTime(now.year, now.month, now.day, 17, 0), // 17:00结束
    );
    
    // 添加测试任务
    await ref.read(timeBoxProvider.notifier).addTask(task15min);
    await ref.read(timeBoxProvider.notifier).addTask(task60min);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F6F3), // Notion风格背景
      appBar: AppBar(
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF37352F),
        elevation: 0,
        title: const Text(
          '时间盒子',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          // Debug: 调试功能按钮（仅在debug模式显示）
          if (kDebugMode) ...[
            // 刷新数据按钮
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                _ensureDataConsistency();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('正在刷新数据...'),
                    duration: Duration(seconds: 1),
                  ),
                );
              },
              tooltip: '刷新数据',
            ),
            // 清除工资统计数据按钮
            IconButton(
              icon: const Icon(Icons.cleaning_services),
              onPressed: _clearWageStatistics,
              tooltip: '清除工资统计数据',
            ),
          ],
          // 添加时间盒子任务按钮
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showCreateTaskDialog,
            tooltip: '添加时间盒子任务',
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          // 更多选项菜单
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              switch (value) {
                case 'embodied_memory':
                  _createEmbodiedMemoryTask();
                  break;
                case 'reset_default_tasks':
                  _resetDefaultTasks();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'embodied_memory',
                child: Row(
                  children: [
                    Icon(
                      Icons.fitness_center,
                      size: 20,
                      color: Color(0xFF0F7B6C),
                    ),
                    SizedBox(width: 12),
                    Text('5分钟具身记忆'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'reset_default_tasks',
                child: Row(
                  children: [
                    Icon(Icons.refresh, size: 20, color: Color(0xFFE03E3E)),
                    SizedBox(width: 12),
                    Text('重置默认任务'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Stack(
        children: [
          Column(
            children: [
              // 统计卡片区域
              Container(
                color: Colors.white,
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // 今日统计 - 使用Consumer确保实时更新
                    Consumer(
                      builder: (context, ref, child) {
                        // 监听timeBoxProvider状态变化以触发重建
                        ref.watch(timeBoxProvider);
                        final currentStats = _currentStats;

                        return Row(
                          children: [
                            Expanded(
                              child: _StatCard(
                                title: _showAllTasks ? '全部任务' : '今日任务',
                                value:
                                    '${currentStats['completedTasks']}/${currentStats['totalTasks']}',
                                icon: Icons.task_alt,
                                color: const Color(0xFF2E7EED),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: _StatCard(
                                title: '预计时长',
                                value: '${currentStats['totalMinutes']}min',
                                icon: Icons.access_time,
                                color: const Color(0xFF7C3AED),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: _StatCard(
                                title: '实际收入',
                                value:
                                    '¥${currentStats['totalWage'].toStringAsFixed(0)}',
                                icon: Icons.monetization_on,
                                color: const Color(0xFF0F7B6C),
                              ),
                            ),
                          ],
                        );
                      },
                    ),

                    // 【开发者测试】动觉记忆法快速入口 - 便于删除的独立区块
                    const SizedBox(height: 12),
                    _buildDeveloperTestSection(),
                    
                    // 【开发者测试】日历时间块测试入口
                    const SizedBox(height: 8),
                    _buildCalendarTestSection(),
                  ],
                ),
              ),
              // 筛选标签栏
              if (_selectedStatus != null ||
                  _selectedPriority != null ||
                  _selectedCategory != null)
                Container(
                  color: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child: Row(
                    children: [
                      const Text(
                        '筛选: ',
                        style: TextStyle(color: Color(0xFF787774)),
                      ),
                      Expanded(
                        child: Wrap(
                          spacing: 8,
                          children: [
                            if (_selectedStatus != null)
                              _FilterChip(
                                label: _selectedStatus!.displayName,
                                onDeleted: () =>
                                    setState(() => _selectedStatus = null),
                              ),
                            if (_selectedPriority != null)
                              _FilterChip(
                                label: _selectedPriority!.displayName,
                                onDeleted: () =>
                                    setState(() => _selectedPriority = null),
                              ),
                            if (_selectedCategory != null)
                              _FilterChip(
                                label: _selectedCategory!,
                                onDeleted: () =>
                                    setState(() => _selectedCategory = null),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              // 任务列表
              Expanded(
                child: _filteredTasks.isEmpty
                    ? _buildEmptyState()
                    : _buildTaskList(),
              ),
            ],
          ),
          // 浮动小窗口
          if (_isFloatingWindowVisible &&
              _globalTimerService.currentTask != null)
            _buildFloatingTimerWindow(),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.inbox_outlined, size: 64, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            '还没有时间盒子',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '点击下方按钮创建第一个学习任务',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  Widget _buildTaskList() {
    // 检查是否有过滤条件，如果有则禁用拖拽重排序
    final hasFilters =
        _selectedStatus != null ||
        _selectedPriority != null ||
        _selectedCategory != null;

    if (hasFilters) {
      // 有过滤或排序时使用普通ListView
      return Column(
        children: [
          // 提示信息
          Container(
            margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, size: 16, color: Colors.blue.shade600),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '拖拽重排序功能在有过滤条件时不可用',
                    style: TextStyle(fontSize: 12, color: Colors.blue.shade700),
                  ),
                ),
              ],
            ),
          ),
          // 任务列表
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.only(left: 16, right: 16, bottom: 8), // 减少底部内边距，保持与ReorderableListView一致
              itemCount: _filteredTasks.length,
              itemBuilder: (context, index) {
                final task = _filteredTasks[index];
                final isLastItem = index == _filteredTasks.length - 1;
                final hasInlineTimer = _globalTimerService.currentTask?.id == task.id &&
                        _globalTimerService.hasActiveTimer;
                
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SwipeableTaskCard(
                      task: task,
                      onTap: () => _showTaskDetailDialog(task),
                      onEdit: () => _showEditTaskDialog(task),
                      onDelete: () => _deleteTask(task),
                      onStatusChanged: (status) {
                        final updatedTask = task.copyWith(
                          status: status,
                          startTime:
                              status == TaskStatus.inProgress &&
                                  task.startTime == null
                              ? DateTime.now()
                              : task.startTime,
                          endTime: status == TaskStatus.completed
                              ? DateTime.now()
                              : task.endTime,
                        );
                        _updateTaskInList(updatedTask);
                      },
                      onStartTimer: _startTimer,
                      customMargin: hasInlineTimer 
                          ? EdgeInsets.zero // 有内联计时器时移除margin，实现相切
                          : null, // 无内联计时器时使用默认margin
                    ),
                    // 如果这个任务正在计时，显示计时器
                    if (hasInlineTimer)
                      _buildInlineTimer(),
                    // 动态控制间距：最后一个任务间距更小，有内联计时器时不添加额外间距
                    if (!isLastItem || !hasInlineTimer) 
                      SizedBox(height: isLastItem ? 4 : 12),
                  ],
                );
              },
            ),
          ),
        ],
      );
    }

    // 无过滤条件时使用ReorderableListView
    final timeBoxState = ref.watch(timeBoxProvider);
    return ReorderableListView.builder(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 8), // 减少底部内边距
      itemCount: timeBoxState.tasks.length,
      onReorder: (oldIndex, newIndex) async {
        if (newIndex > oldIndex) newIndex--;
        final tasks = List<TimeBoxTask>.from(timeBoxState.tasks);
        final item = tasks.removeAt(oldIndex);
        tasks.insert(newIndex, item);
        // 这里可以添加重新排序的逻辑，暂时不实现
      },
      itemBuilder: (context, index) {
        final task = timeBoxState.tasks[index];
        final isLastItem = index == timeBoxState.tasks.length - 1;
        final hasInlineTimer = _globalTimerService.currentTask?.id == task.id &&
                _globalTimerService.hasActiveTimer;
        
        return Column(
          key: ValueKey(task.id),
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SwipeableTaskCard(
              task: task,
              onTap: () => _showTaskDetailDialog(task),
              onEdit: () => _showEditTaskDialog(task),
              onDelete: () => _deleteTask(task),
              onStatusChanged: (status) {
                final updatedTask = task.copyWith(
                  status: status,
                  startTime:
                      status == TaskStatus.inProgress && task.startTime == null
                      ? DateTime.now()
                      : task.startTime,
                  endTime: status == TaskStatus.completed
                      ? DateTime.now()
                      : task.endTime,
                );
                _updateTaskInList(updatedTask);
              },
              onStartTimer: _startTimer,
              customMargin: hasInlineTimer 
                  ? EdgeInsets.zero // 有内联计时器时移除margin，实现相切
                  : null, // 无内联计时器时使用默认margin
            ),
            // 如果这个任务正在计时，显示计时器
            if (hasInlineTimer)
              _buildInlineTimer(),
            // 动态控制间距：最后一个任务的间距更小，有内联计时器时不添加额外间距
            if (!isLastItem || !hasInlineTimer) 
              SizedBox(height: isLastItem ? 4 : 12), // 最后一个任务间距更小
          ],
        );
      },
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('筛选任务'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 显示范围选择
            const Text('显示范围', style: TextStyle(fontWeight: FontWeight.w600)),
            SwitchListTile(
              title: const Text('显示所有任务'),
              subtitle: Text(_showAllTasks ? '显示所有任务' : '仅显示今日任务'),
              value: _showAllTasks,
              onChanged: (value) {
                setState(() {
                  _showAllTasks = value;
                });
              },
            ),
            const SizedBox(height: 16),
            const Text('状态', style: TextStyle(fontWeight: FontWeight.w600)),
            Wrap(
              spacing: 8,
              children: TaskStatus.values.map((status) {
                return FilterChip(
                  label: Text(status.displayName),
                  selected: _selectedStatus == status,
                  onSelected: (selected) {
                    setState(() {
                      _selectedStatus = selected ? status : null;
                    });
                  },
                );
              }).toList(),
            ),
            const SizedBox(height: 16),
            const Text('优先级', style: TextStyle(fontWeight: FontWeight.w600)),
            Wrap(
              spacing: 8,
              children: TaskPriority.values.map((priority) {
                return FilterChip(
                  label: Text(priority.displayName),
                  selected: _selectedPriority == priority,
                  onSelected: (selected) {
                    setState(() {
                      _selectedPriority = selected ? priority : null;
                    });
                  },
                );
              }).toList(),
            ),
            const SizedBox(height: 16),
            const Text('学科分类', style: TextStyle(fontWeight: FontWeight.w600)),
            Wrap(
              spacing: 8,
              children: ['计算机科学', '数学', '英语', '政治'].map((category) {
                final categoryColor = _getCategoryColor(category);
                return FilterChip(
                  avatar: CircleAvatar(
                    backgroundColor: categoryColor,
                    radius: 8,
                  ),
                  label: Text(category),
                  selected: _selectedCategory == category,
                  onSelected: (selected) {
                    setState(() {
                      _selectedCategory = selected ? category : null;
                    });
                  },
                );
              }).toList(),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showCreateTaskDialog() {
    showDialog(
      context: context,
      builder: (context) => TaskCreateDialog(
        onTaskCreated: (task) async {
          await ref.read(timeBoxProvider.notifier).addTask(task);

          // 确保UI同步更新
          if (mounted) {
            setState(() {
              // 触发UI重建以反映新任务
            });

            // 输出调试信息
            final stats = _currentStats;
            print(
              '📊 创建任务后统计 - 总任务: ${stats['totalTasks']}, 已完成: ${stats['completedTasks']}',
            );
          }
        },
      ),
    );
  }

  /// 重置默认任务
  void _resetDefaultTasks() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('重置默认任务'),
        content: const Text('这将删除所有现有的默认任务并重新创建。确定要继续吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('确定', style: TextStyle(color: Color(0xFFE03E3E))),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // 删除所有默认任务
        final defaultTaskIds = ['1', '2', '3', '4', '5'];
        for (final id in defaultTaskIds) {
          await ref.read(timeBoxProvider.notifier).deleteTask(id);
        }

        // 强制刷新数据以重新创建默认任务
        await ref.read(timeBoxProvider.notifier).refresh();

        if (mounted) {
          setState(() {});
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('默认任务已重置'),
              backgroundColor: Color(0xFF0F7B6C),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('重置失败: $e'), backgroundColor: Colors.red),
          );
        }
      }
    }
  }

  /// 创建5分钟具身记忆任务
  void _createEmbodiedMemoryTask() async {
    final task = TimeBoxTask(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: '具身记忆训练',
      description: '5分钟动觉记忆活动，结合身体动作记忆单词',
      plannedMinutes: 5,
      status: TaskStatus.pending,
      priority: TaskPriority.low,
      category: '英语',
      createdAt: DateTime.now(),
    );

    await ref.read(timeBoxProvider.notifier).addTask(task);

    // 确保UI同步更新
    if (mounted) {
      setState(() {
        // 触发UI重建以反映新任务
      });

      // 输出调试信息
      final stats = _currentStats;
      print(
        '📊 添加任务后统计 - 总任务: ${stats['totalTasks']}, 已完成: ${stats['completedTasks']}',
      );

      // 显示成功提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Row(
            children: [
              Icon(Icons.fitness_center, color: Colors.white, size: 20),
              SizedBox(width: 8),
              Text('已添加5分钟具身记忆任务'),
            ],
          ),
          backgroundColor: const Color(0xFF0F7B6C),
          duration: const Duration(seconds: 2),
          action: SnackBarAction(
            label: '立即开始',
            textColor: Colors.white,
            onPressed: () {
              _startTimer(task);
            },
          ),
        ),
      );
    }
  }

  void _showTaskDetailDialog(TimeBoxTask task) {
    showDialog(
      context: context,
      builder: (context) => TaskDetailDialog(
        task: task,
        onTaskUpdated: (updatedTask) async {
          await ref.read(timeBoxProvider.notifier).updateTask(updatedTask);
        },
        onStartTimer: _startTimer,
      ),
    );
  }

  void _showEditTaskDialog(TimeBoxTask task) {
    showDialog(
      context: context,
      builder: (context) => TaskEditDialog(
        task: task,
        onTaskUpdated: (updatedTask) async {
          await ref.read(timeBoxProvider.notifier).updateTask(updatedTask);
        },
      ),
    );
  }

  void _deleteTask(TimeBoxTask task) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除"${task.title}"吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('删除', style: TextStyle(color: Color(0xFFE7433A))),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await ref.read(timeBoxProvider.notifier).deleteTask(task.id);

      if (mounted) {
        setState(() {
          // 触发UI重建以反映任务删除
        });

        // 输出调试信息
        final stats = _currentStats;
        print(
          '📊 删除任务后统计 - 总任务: ${stats['totalTasks']}, 已完成: ${stats['completedTasks']}',
        );

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已删除"${task.title}"'),
            action: SnackBarAction(
              label: '撤销',
              onPressed: () async {
                await ref.read(timeBoxProvider.notifier).addTask(task);
                if (mounted) {
                  setState(() {
                    // 触发UI重建以反映任务恢复
                  });
                }
              },
            ),
          ),
        );
      }
    }
  }
}

// 统计卡片组件
class _StatCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  const _StatCard({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
          Text(
            title,
            style: const TextStyle(fontSize: 12, color: Color(0xFF787774)),
          ),
        ],
      ),
    );
  }
}

// 筛选标签组件
class _FilterChip extends StatelessWidget {
  final String label;
  final VoidCallback onDeleted;

  const _FilterChip({required this.label, required this.onDeleted});

  @override
  Widget build(BuildContext context) {
    return Chip(
      label: Text(label),
      onDeleted: onDeleted,
      backgroundColor: const Color(0xFF2E7EED).withValues(alpha: 0.1),
      labelStyle: const TextStyle(color: Color(0xFF2E7EED), fontSize: 12),
      deleteIconColor: const Color(0xFF2E7EED),
    );
  }
}

// 数据模型和枚举定义已移至 models/timebox_models.dart

// 时间盒子卡片组件
class TimeBoxCard extends StatelessWidget {
  final TimeBoxTask task;
  final VoidCallback onTap;
  final Function(TaskStatus) onStatusChanged;
  final Function(TimeBoxTask)? onStartTimer; // 新增：开始计时回调

  const TimeBoxCard({
    super.key,
    required this.task,
    required this.onTap,
    required this.onStatusChanged,
    this.onStartTimer, // 可选参数
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        elevation: 0,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade200),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        task.title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF37352F),
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: task.priority.color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        task.priority.displayName,
                        style: TextStyle(
                          fontSize: 10,
                          color: task.priority.color,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                if (task.description.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    task.description,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF787774),
                    ),
                  ),
                ],
                const SizedBox(height: 12),
                Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 16,
                      color: Colors.grey.shade600,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${task.plannedMinutes}分钟',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: SubjectColors.getCategoryColor(task.category),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        task.category,
                        style: const TextStyle(
                          fontSize: 11,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Icon(
                      Icons.monetization_on,
                      size: 16,
                      color: Colors.grey.shade600,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '¥${task.calculateWage().toStringAsFixed(0)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const Spacer(),
                    // 根据任务状态显示开始按钮或状态标签
                    if (onStartTimer != null &&
                        (task.status == TaskStatus.pending ||
                            task.status == TaskStatus.inProgress))
                      ElevatedButton(
                        onPressed: () => onStartTimer!(task),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF2E7EED),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          minimumSize: const Size(60, 28),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        child: Text(
                          task.status == TaskStatus.pending ? '开始' : '继续',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      )
                    else
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: task.status.color.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          task.status.displayName,
                          style: TextStyle(
                            fontSize: 12,
                            color: task.status.color,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// 创建任务对话框
class TaskCreateDialog extends StatefulWidget {
  final Function(TimeBoxTask) onTaskCreated;

  const TaskCreateDialog({super.key, required this.onTaskCreated});

  @override
  State<TaskCreateDialog> createState() => _TaskCreateDialogState();
}

class _TaskCreateDialogState extends State<TaskCreateDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _minutesController = TextEditingController(text: '60');
  final TaskCategoryManager _categoryManager = taskCategoryManager;

  TaskPriority _priority = TaskPriority.medium;
  String _category = '';
  // Note: _isLoading field removed as it was unused
  // bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    await _categoryManager.loadFromStorage();
    if (mounted) {
      setState(() {
        // Note: _isLoading = false; commented out as field was removed
        // 设置默认分类为第一个可用分类
        if (_categoryManager.categories.isNotEmpty) {
          _category = _categoryManager.categories.first.name;
        }
      });
    }
  }

  /// 获取优先级对应的颜色
  Color _getPriorityColor(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.high:
        return const Color(0xFFE03E3E); // 红色
      case TaskPriority.medium:
        return const Color(0xFFFFD700); // 黄色
      case TaskPriority.low:
        return const Color(0xFF0F7B6C); // 绿色
    }
  }

  /// 获取优先级对应的图标
  IconData _getPriorityIcon(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.high:
        return Icons.priority_high;
      case TaskPriority.medium:
        return Icons.remove;
      case TaskPriority.low:
        return Icons.low_priority;
    }
  }

  /// 获取分类对应的图标
  IconData _getCategoryIcon(String category) {
    switch (category) {
      case '计算机科学':
        return Icons.computer;
      case '数学':
        return Icons.calculate;
      case '英语':
        return Icons.language;
      case '政治':
        return Icons.account_balance;
      case '休息':
        return Icons.fitness_center;
      default:
        return Icons.category;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 4,
      shadowColor: Colors.black.withValues(alpha: 0.1),
      title: const Text(
        '创建时间盒子',
        style: TextStyle(
          color: Color(0xFF37352F),
          fontWeight: FontWeight.w600,
          fontSize: 18,
        ),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 任务标题输入框
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: '任务标题',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '请输入任务标题';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // 任务描述输入框
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: '任务描述（可选）',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),

              // 预计时长输入框
              TextFormField(
                controller: _minutesController,
                decoration: const InputDecoration(
                  labelText: '预计时长（分钟）',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '请输入预计时长';
                  }
                  if (int.tryParse(value) == null || int.parse(value) <= 0) {
                    return '请输入有效的时长';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // 优先级选择器
              Theme(
                data: Theme.of(context).copyWith(
                  // 禁用Material 3.0的surface tinting，确保纯白色背景
                  popupMenuTheme: const PopupMenuThemeData(
                    surfaceTintColor:
                        Colors.transparent, // 关键：禁用surface tinting
                  ),
                ),
                child: DropdownButtonFormField<TaskPriority>(
                  value: _priority,
                  decoration: const InputDecoration(
                    labelText: '优先级',
                    border: OutlineInputBorder(),
                  ),
                  dropdownColor: const Color(0xFFFFFFFF), // 使用明确的纯白色
                  items: TaskPriority.values.map((priority) {
                    final priorityColor = _getPriorityColor(priority);
                    final priorityIcon = _getPriorityIcon(priority);
                    return DropdownMenuItem(
                      value: priority,
                      child: Row(
                        children: [
                          Icon(priorityIcon, size: 16, color: priorityColor),
                          const SizedBox(width: 8),
                          Text(priority.displayName),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _priority = value!;
                    });
                  },
                ),
              ),
              const SizedBox(height: 16),

              // 学科分类选择器
              Theme(
                data: Theme.of(context).copyWith(
                  // 禁用Material 3.0的surface tinting，确保纯白色背景
                  popupMenuTheme: const PopupMenuThemeData(
                    surfaceTintColor:
                        Colors.transparent, // 关键：禁用surface tinting
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _category.isEmpty ? null : _category,
                        decoration: const InputDecoration(
                          labelText: '任务分类',
                          border: OutlineInputBorder(),
                        ),
                        dropdownColor: const Color(0xFFFFFFFF),
                        items: _categoryManager.categories.map((category) {
                          final categoryColor = _categoryManager
                              .getCategoryColor(category.name);
                          final categoryIcon = _getCategoryIcon(category.name);
                          return DropdownMenuItem(
                            value: category.name,
                            child: Row(
                              children: [
                                CircleAvatar(
                                  backgroundColor: categoryColor,
                                  radius: 6,
                                ),
                                const SizedBox(width: 8),
                                Icon(
                                  categoryIcon,
                                  size: 16,
                                  color: categoryColor,
                                ),
                                const SizedBox(width: 8),
                                Text(category.name),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _category = value!;
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      icon: const Icon(Icons.settings),
                      onPressed: () {
                        Navigator.of(context)
                            .push(
                              MaterialPageRoute(
                                builder: (context) =>
                                    const TaskCategoryManagementPage(),
                              ),
                            )
                            .then((_) {
                              // 重新加载分类
                              _loadCategories();
                            });
                      },
                      tooltip: '管理分类',
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      actionsPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      actions: [
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                '取消',
                style: TextStyle(color: Color(0xFF9B9A97)),
              ),
            ),
            const SizedBox(width: 12),
            ElevatedButton(
              onPressed: () {
                if (_formKey.currentState!.validate()) {
                  final task = TimeBoxTask(
                    id: DateTime.now().millisecondsSinceEpoch.toString(),
                    title: _titleController.text.trim(),
                    description: _descriptionController.text.trim(),
                    plannedMinutes: int.parse(_minutesController.text),
                    status: TaskStatus.pending,
                    priority: _priority,
                    category: _category,
                    createdAt: DateTime.now(),
                  );
                  widget.onTaskCreated(task);
                  Navigator.of(context).pop();
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2E7EED),
                foregroundColor: Colors.white,
                minimumSize: const Size(80, 48),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('创建'),
            ),
          ],
        ),
      ],
    );
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _minutesController.dispose();
    super.dispose();
  }
}

// 任务详情对话框
class TaskDetailDialog extends StatefulWidget {
  final TimeBoxTask task;
  final Function(TimeBoxTask) onTaskUpdated;
  final Function(TimeBoxTask) onStartTimer;

  const TaskDetailDialog({
    super.key,
    required this.task,
    required this.onTaskUpdated,
    required this.onStartTimer,
  });

  @override
  State<TaskDetailDialog> createState() => _TaskDetailDialogState();
}

class _TaskDetailDialogState extends State<TaskDetailDialog> {
  late TimeBoxTask _task;

  @override
  void initState() {
    super.initState();
    _task = widget.task;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(_task.title),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (_task.description.isNotEmpty) ...[
              const Text('描述：', style: TextStyle(fontWeight: FontWeight.w600)),
              Text(_task.description),
              const SizedBox(height: 16),
            ],
            Text('预计时长：${_task.plannedMinutes}分钟'),
            const SizedBox(height: 8),
            Row(
              children: [
                const Text('分类：'),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: SubjectColors.getCategoryColor(_task.category),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    _task.category,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Text('优先级：'),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: _task.priority.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    _task.priority.displayName,
                    style: TextStyle(color: _task.priority.color, fontSize: 12),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Text('状态：'),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: _task.status.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    _task.status.displayName,
                    style: TextStyle(color: _task.status.color, fontSize: 12),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text('预估工资：¥${_task.calculateWage().toStringAsFixed(0)}'),
            if ((_task.actualMinutes ?? 0) > 0) ...[
              const SizedBox(height: 8),
              Text('实际时长：${_task.actualMinutes}分钟'),
            ],
            const SizedBox(height: 16),
            const Text('状态管理：', style: TextStyle(fontWeight: FontWeight.w600)),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: TaskStatus.values.map((status) {
                return ActionChip(
                  label: Text(status.displayName),
                  backgroundColor: _task.status == status
                      ? status.color.withValues(alpha: 0.2)
                      : Colors.grey.shade100,
                  onPressed: () {
                    setState(() {
                      _task = _task.copyWith(
                        status: status,
                        startTime:
                            status == TaskStatus.inProgress &&
                                _task.startTime == null
                            ? DateTime.now()
                            : _task.startTime,
                        endTime:
                            status == TaskStatus.completed &&
                                _task.endTime == null
                            ? DateTime.now()
                            : _task.endTime,
                      );
                    });
                  },
                );
              }).toList(),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('关闭'),
        ),
        if (_task.status == TaskStatus.pending ||
            _task.status == TaskStatus.inProgress)
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              widget.onTaskUpdated(_task);
              // 启动内嵌计时器
              widget.onStartTimer(_task);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2E7EED),
              foregroundColor: Colors.white,
            ),
            child: Text(_task.status == TaskStatus.pending ? '开始学习' : '继续学习'),
          ),
        TextButton(
          onPressed: () {
            widget.onTaskUpdated(_task);
            Navigator.of(context).pop();
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(const SnackBar(content: Text('任务已更新')));
          },
          child: const Text('保存更改'),
        ),
      ],
    );
  }
}

// 动觉学习页面
class KinestheticLearningPage extends StatefulWidget {
  final VoidCallback onComplete;
  final VoidCallback? onSkipRest; // 新增：跳过休息时间的回调

  const KinestheticLearningPage({
    super.key,
    required this.onComplete,
    this.onSkipRest, // 可选参数
  });

  @override
  State<KinestheticLearningPage> createState() =>
      _KinestheticLearningPageState();
}

class _KinestheticLearningPageState extends State<KinestheticLearningPage>
    with TickerProviderStateMixin {
  final Random _random = Random(); // 添加随机数生成器
  final VocabularyService _vocabularyService = VocabularyService(); // 添加词汇服务
  final CustomActionLibraryService _customLibraryService =
      CustomActionLibraryService(); // 自定义动作库服务
  PAOIntegrationService? _paoService; // PAO集成服务
  bool _isGenerating = false; // 防止重复生成单词
  int _learnedWords = 0;
  String _currentWord = 'account';
  String _currentDefinition = 'n. 账户; 解释; 描述; 理由 vi. 解释; 导致; 报账';
  List<String> _selectedLetters = ['A', 'C', 'C', 'O', 'U', 'N', 'T'];

  // 全局计时器服务
  final GlobalTimerService _globalTimerService = GlobalTimerService();

  // 动作数据现在通过PAO集成服务获取

  final List<Map<String, String>> _vocabularyBank = [
    {'word': 'account', 'definition': 'n. 账户; 解释; 描述; 理由 vi. 解释; 导致; 报账'},
    {'word': 'achieve', 'definition': 'v. 实现; 取得; 获得; 成功'},
    {'word': 'benefit', 'definition': 'n. 利益; 好处; 优势 v. 有利于; 受益'},
    {'word': 'complex', 'definition': 'adj. 复杂的; 复合的 n. 综合体; 情结'},
    {'word': 'develop', 'definition': 'v. 发展; 开发; 发育; 培养'},
    {'word': 'element', 'definition': 'n. 元素; 要素; 成分; 因子'},
    {'word': 'factor', 'definition': 'n. 因素; 要素; 因子 v. 把...因式分解'},
    {'word': 'general', 'definition': 'adj. 一般的; 普遍的; 总的 n. 将军'},
  ];

  @override
  void initState() {
    super.initState();
    _initializePAOService();
    _generateRandomWord();

    // 监听全局计时器状态变化
    _globalTimerService.addListener(_onTimerStateChanged);
  }

  /// 初始化PAO集成服务
  void _initializePAOService() async {
    await _customLibraryService.loadFromStorage();
    _paoService = PAOIntegrationService(_customLibraryService);
  }

  /// 全局计时器状态变化监听
  void _onTimerStateChanged() {
    if (mounted) {
      setState(() {
        // 检查休息时间是否结束
        if (_globalTimerService.pomodoroState != PomodoroTimerState.rest) {
          print('🏁 休息时间结束，自动返回');
          Future.delayed(const Duration(milliseconds: 100), () {
            widget.onComplete();
          });
        }
      });
    }
  }

  void _generateRandomWord() async {
    if (_isGenerating) return; // 防止重复调用

    _isGenerating = true;

    try {
      // 获取用户自定义词汇（记忆词库）
      final memoryWords = await _vocabularyService.getMemoryVocabularyWords();

      // 获取用户已选择的考研词汇
      final selectedWords = await _vocabularyService
          .getSelectedWordsFromProgress();

      // 合并所有可用单词，优先使用自定义词汇
      final allAvailableWords = <String>[];
      allAvailableWords.addAll(memoryWords);
      allAvailableWords.addAll(selectedWords);

      if (allAvailableWords.isNotEmpty) {
        final randomIndex = _random.nextInt(allAvailableWords.length);
        final selectedWordKey = allAvailableWords[randomIndex];

        // 检查是否是自定义词汇
        if (memoryWords.contains(selectedWordKey)) {
          // 使用自定义词汇
          final memoryVocab = await _vocabularyService.getMemoryVocabulary();
          final wordData =
              memoryVocab[selectedWordKey.toLowerCase()]
                  as Map<String, dynamic>?;

          if (wordData != null) {
            setState(() {
              _currentWord = wordData['word'] as String;
              _currentDefinition = (wordData['definition'] as String).isNotEmpty
                  ? wordData['definition'] as String
                  : '自定义词汇'; // 如果没有定义，显示默认文本
              // 获取单词中的不重复字母
              _selectedLetters = _currentWord
                  .toUpperCase()
                  .split('')
                  .toSet()
                  .toList();
            });
          } else {
            _useFallbackWord();
          }
        } else {
          // 使用考研词汇
          final vocabulary = await _vocabularyService.loadVocabulary();
          final wordDetails = vocabulary.words[selectedWordKey];

          if (wordDetails != null) {
            setState(() {
              _currentWord = selectedWordKey;
              _currentDefinition = wordDetails.definition;
              // 获取单词中的不重复字母
              _selectedLetters = _currentWord
                  .toUpperCase()
                  .split('')
                  .toSet()
                  .toList();
            });
          } else {
            _useFallbackWord();
          }
        }
      } else {
        // 如果用户没有选择任何单词，使用默认词汇库
        _useFallbackWord();
      }
    } catch (e) {
      // 出错时使用默认词汇库
      _useFallbackWord();
    }

    // 短暂延迟后重置标志，防止过快连续点击
    Future.delayed(const Duration(milliseconds: 300), () {
      _isGenerating = false;
    });
  }

  /// 使用默认词汇库
  void _useFallbackWord() {
    final randomIndex = _random.nextInt(_vocabularyBank.length);
    final selectedWord = _vocabularyBank[randomIndex];

    setState(() {
      _currentWord = selectedWord['word']!;
      _currentDefinition = selectedWord['definition']!;
      // 获取单词中的不重复字母
      _selectedLetters = _currentWord.toUpperCase().split('').toSet().toList();
    });
  }

  String _formatRestTime(int seconds) {
    final minutes = seconds ~/ 60;
    final secs = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }

  /// 显示动作库选择器
  void _showLibrarySelector() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: const Color(0xFFE3E2E0),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              '选择动作库',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 16),
            ...(_paoService?.getAvailableLibraryOptions() ?? []).map((option) {
              return ListTile(
                leading: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: option['isSelected']
                        ? const Color(0xFF2E7EED).withValues(alpha: 0.1)
                        : const Color(0xFFF7F6F3),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: option['isSelected']
                          ? const Color(0xFF2E7EED)
                          : const Color(0xFFE3E2E0),
                    ),
                  ),
                  child: Icon(
                    option['type'] == 'preset'
                        ? Icons.fitness_center
                        : Icons.folder,
                    color: option['isSelected']
                        ? const Color(0xFF2E7EED)
                        : const Color(0xFF9B9A97),
                    size: 20,
                  ),
                ),
                title: Text(
                  option['name'],
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF37352F),
                  ),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      option['description'],
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF9B9A97),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${option['completedActions']}/26 动作',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF0F7B6C),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                trailing: option['isSelected']
                    ? const Icon(Icons.check_circle, color: Color(0xFF2E7EED))
                    : null,
                onTap: () async {
                  final scaffoldMessenger = ScaffoldMessenger.of(context);
                  final optionName = option['name'];
                  Navigator.pop(context);
                  await _paoService?.switchToCustomLibrary(option['id']);
                  setState(() {
                    // 重新生成单词以使用新的动作库
                    _generateRandomWord();
                  });

                  if (mounted) {
                    scaffoldMessenger.showSnackBar(
                      SnackBar(
                        content: Text('已切换到"$optionName"'),
                        backgroundColor: const Color(0xFF0F7B6C),
                      ),
                    );
                  }
                },
              );
            }),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _globalTimerService.removeListener(_onTimerStateChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F6F3),
      appBar: AppBar(
        backgroundColor: const Color(0xFF2E7EED),
        foregroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: widget.onComplete,
        ),
        title: const Text(
          '动觉记忆法',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
        ),
        centerTitle: true,
        actions: [
          // 动作库选择按钮
          IconButton(
            icon: const Icon(Icons.library_books),
            onPressed: _showLibrarySelector,
            tooltip: '选择动作库',
          ),
          Center(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              margin: const EdgeInsets.only(right: 16),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                _formatRestTime(_globalTimerService.remainingSeconds),
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // 单词显示区域 - Notion风格
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: const Color(0xFFE3E2DE)),
            ),
            child: Column(
              children: [
                Text(
                  _currentWord.toLowerCase(),
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF37352F),
                    letterSpacing: 1,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  '/${_currentWord.toLowerCase()}/',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF9B9A97),
                    fontStyle: FontStyle.italic,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  _currentDefinition,
                  style: const TextStyle(
                    fontSize: 13,
                    color: Color(0xFF37352F),
                    height: 1.3,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                // 动作库信息
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: const Color(0xFF2E7EED).withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        (_paoService?.hasCustomLibrarySelected ?? false)
                            ? Icons.folder
                            : Icons.fitness_center,
                        size: 12,
                        color: const Color(0xFF2E7EED),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _paoService?.getCurrentLibraryInfo()['name'] ?? '默认动作库',
                        style: const TextStyle(
                          fontSize: 11,
                          color: Color(0xFF2E7EED),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // 字母键盘布局
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: const Color(0xFFE3E2DE)),
            ),
            child: Column(
              children: [
                // 第一行: Q W E R T Y U I O P
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P']
                      .map((letter) => _buildKeyboardLetterButton(letter))
                      .toList(),
                ),
                const SizedBox(height: 8),
                // 第二行: A S D F G H J K L
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L']
                      .map((letter) => _buildKeyboardLetterButton(letter))
                      .toList(),
                ),
                const SizedBox(height: 8),
                // 第三行: Z X C V B N M
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: ['Z', 'X', 'C', 'V', 'B', 'N', 'M']
                      .map((letter) => _buildKeyboardLetterButton(letter))
                      .toList(),
                ),
              ],
            ),
          ),

          // 主要内容区域
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  const SizedBox(height: 16),

                  // 运动列表 - 紧凑设计
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 12),
                    child: Column(
                      children: _selectedLetters.map((letter) {
                        final paoExercise = _paoService?.getExerciseForLetter(
                          letter,
                        );
                        if (paoExercise == null) return const SizedBox.shrink();

                        // 转换为旧格式以保持兼容性
                        final exercise = {
                          'name': paoExercise.nameEn,
                          'chinese': paoExercise.nameCn,
                          'description': paoExercise.description,
                        };
                        return Container(
                          margin: const EdgeInsets.only(bottom: 6),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(6),
                            border: Border.all(color: Colors.grey.shade200),
                          ),
                          child: Row(
                            children: [
                              Container(
                                width: 24,
                                height: 24,
                                decoration: BoxDecoration(
                                  color: const Color(0xFF2E7EED),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Center(
                                  child: Text(
                                    letter,
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  '${exercise['name']} (${exercise['chinese']})',
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: Color(0xFF37352F),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // 底部操作栏 - 紧凑设计
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  spreadRadius: 0,
                  blurRadius: 4,
                  offset: const Offset(0, -1),
                ),
              ],
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          setState(() {
                            _learnedWords++;
                          });
                          _generateRandomWord();
                        },
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          side: const BorderSide(color: Color(0xFF0F7B6C)),
                        ),
                        child: const Text(
                          '认识',
                          style: TextStyle(
                            color: Color(0xFF0F7B6C),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          setState(() {
                            _learnedWords++;
                          });
                          _generateRandomWord();
                        },
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          side: const BorderSide(color: Color(0xFFE03E3E)),
                        ),
                        child: const Text(
                          '不认识',
                          style: TextStyle(
                            color: Color(0xFFE03E3E),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: widget.onComplete,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF2E7EED),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    child: const Text(
                      '完成学习，继续工作',
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                // 提前结束休息按钮（仅在有回调时显示）
                if (widget.onSkipRest != null)
                  SizedBox(
                    width: double.infinity,
                    child: TextButton(
                      onPressed: () {
                        widget.onSkipRest!();
                        // 注意：不在这里调用 Navigator.pop()，
                        // 因为导航逻辑已经在 onSkipRest 回调中处理
                      },
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                      child: const Text(
                        '提前结束休息，立即工作',
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFF9B9A97),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                const SizedBox(height: 6),
                Text(
                  '已学习：$_learnedWords 个单词',
                  style: const TextStyle(
                    fontSize: 11,
                    color: Color(0xFF787774),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildKeyboardLetterButton(String letter) {
    // 检查当前单词中是否包含该字母
    final isHighlighted = _currentWord.toUpperCase().contains(letter);
    return Container(
      width: 28,
      height: 32,
      margin: const EdgeInsets.symmetric(horizontal: 1.5),
      decoration: BoxDecoration(
        color: isHighlighted ? const Color(0xFF2E7EED) : Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: isHighlighted
              ? const Color(0xFF2E7EED)
              : const Color(0xFFE3E2DE),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            offset: const Offset(0, 1),
            blurRadius: 1,
          ),
        ],
      ),
      child: Center(
        child: Text(
          letter,
          style: TextStyle(
            color: isHighlighted ? Colors.white : const Color(0xFF37352F),
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
        ),
      ),
    );
  }
}

// 可滑动任务卡片组件（类似微信聊天界面的左滑操作）
class SwipeableTaskCard extends StatefulWidget {
  final TimeBoxTask task;
  final VoidCallback onTap;
  final VoidCallback onEdit;
  final VoidCallback onDelete;
  final Function(TaskStatus) onStatusChanged;
  final Function(TimeBoxTask)? onStartTimer;
  final EdgeInsets? customMargin; // 新增：自定义margin参数

  const SwipeableTaskCard({
    super.key,
    required this.task,
    required this.onTap,
    required this.onEdit,
    required this.onDelete,
    required this.onStatusChanged,
    this.onStartTimer,
    this.customMargin, // 新增参数
  });

  @override
  State<SwipeableTaskCard> createState() => _SwipeableTaskCardState();
}

class _SwipeableTaskCardState extends State<SwipeableTaskCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  // late Animation<double> _buttonOpacityAnimation; // 暂未使用

  double _dragOffset = 0.0;
  bool _isSwipeOpen = false;
  static const double _maxSwipeDistance = 120.0; // 最大滑动距离
  static const double _actionButtonWidth = 60.0; // 每个操作按钮的宽度
  // static const double _triggerThreshold = 15.0; // 降低阈值，更早显示按钮 (暂未使用)

  // TimeBoxCard的样式常量（与原卡片保持一致）
  static const double _cardBorderRadius = 8.0;
  static const EdgeInsets _cardMargin = EdgeInsets.only(bottom: 12);
  static const EdgeInsets _cardPadding = EdgeInsets.all(16);

  @override
  void initState() {
    super.initState();

    // 使用单一动画控制器，简化动画逻辑
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200), // 缩短动画时长
      vsync: this,
    );

    // 滑动动画
    _slideAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut, // 使用更简单的缓动曲线
      ),
    );

    // 按钮透明度动画 (暂未使用)
    /*
    _buttonOpacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
    */
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handlePanStart(DragStartDetails details) {
    _animationController.stop();
  }

  void _handlePanUpdate(DragUpdateDetails details) {
    setState(() {
      // 只允许向左滑动（显示操作按钮）
      _dragOffset = (_dragOffset + details.delta.dx).clamp(
        -_maxSwipeDistance,
        0.0,
      );
    });
  }

  void _handlePanEnd(DragEndDetails details) {
    final velocity = details.velocity.pixelsPerSecond.dx;

    if (_dragOffset < -_maxSwipeDistance / 2 || velocity < -500) {
      // 滑动距离超过一半或快速左滑，显示操作按钮
      _openSwipe();
    } else {
      // 否则回弹到原位
      _closeSwipe();
    }
  }

  void _openSwipe() {
    setState(() {
      _isSwipeOpen = true;
      _dragOffset = -_maxSwipeDistance;
    });
    _animationController.forward();
    HapticFeedback.lightImpact(); // 添加触觉反馈
  }

  void _closeSwipe() {
    setState(() {
      _isSwipeOpen = false;
      _dragOffset = 0.0;
    });
    _animationController.reverse();
  }

  void _handleTapOutside() {
    if (_isSwipeOpen) {
      _closeSwipe();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _handleTapOutside,
      child: Container(
        margin: widget.customMargin ?? _cardMargin, // 优先使用自定义margin
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            // 计算实际的滑动偏移量
            final slideOffset = _isSwipeOpen
                ? -_maxSwipeDistance * _slideAnimation.value
                : _dragOffset;

            // 计算滑动进度（0.0 到 1.0）
            final swipeProgress = (_dragOffset.abs() / _maxSwipeDistance).clamp(
              0.0,
              1.0,
            );

            return Stack(
              children: [
                // 渐进式展开的操作按钮
                if (_dragOffset < 0 || _isSwipeOpen)
                  Positioned.fill(
                    child: _buildProgressiveActionButtons(swipeProgress),
                  ),
                // 主要任务卡片
                Transform.translate(
                  offset: Offset(slideOffset, 0),
                  child: GestureDetector(
                    onPanStart: _handlePanStart,
                    onPanUpdate: _handlePanUpdate,
                    onPanEnd: _handlePanEnd,
                    child: _buildTaskCardWithoutMargin(),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  // 构建简化的操作按钮（避免复杂的布局约束）
  Widget _buildProgressiveActionButtons(double progress) {
    return SizedBox(
      height: double.infinity,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // 编辑按钮
          _buildSimpleActionButton(
            width: _actionButtonWidth,
            color: const Color(0xFF3177E2),
            icon: Icons.edit,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(_cardBorderRadius),
              bottomLeft: Radius.circular(_cardBorderRadius),
            ),
            onTap: () {
              HapticFeedback.selectionClick();
              _closeSwipe();
              widget.onEdit();
            },
          ),
          // 删除按钮
          _buildSimpleActionButton(
            width: _actionButtonWidth,
            color: const Color(0xFFE7433A),
            icon: Icons.close,
            borderRadius: const BorderRadius.only(
              topRight: Radius.circular(_cardBorderRadius),
              bottomRight: Radius.circular(_cardBorderRadius),
            ),
            onTap: () {
              HapticFeedback.selectionClick();
              _closeSwipe();
              widget.onDelete();
            },
          ),
        ],
      ),
    );
  }

  // 构建简化的单个操作按钮
  Widget _buildSimpleActionButton({
    required double width,
    required Color color,
    required IconData icon,
    required BorderRadius borderRadius,
    required VoidCallback onTap,
  }) {
    return Container(
      width: width,
      height: double.infinity,
      decoration: BoxDecoration(color: color, borderRadius: borderRadius),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius,
          child: Center(child: Icon(icon, color: Colors.white, size: 20)),
        ),
      ),
    );
  }

  // 构建无margin的TimeBoxCard，确保高度完全匹配
  Widget _buildTaskCardWithoutMargin() {
    return Material(
      color: Colors.white,
      borderRadius: BorderRadius.circular(_cardBorderRadius),
      elevation: 0,
      child: InkWell(
        onTap: widget.onTap,
        borderRadius: BorderRadius.circular(_cardBorderRadius),
        child: Container(
          padding: _cardPadding,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade200),
            borderRadius: BorderRadius.circular(_cardBorderRadius),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      widget.task.title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF37352F),
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: widget.task.priority.color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      widget.task.priority.displayName,
                      style: TextStyle(
                        fontSize: 10,
                        color: widget.task.priority.color,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              if (widget.task.description.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  widget.task.description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF787774),
                  ),
                ),
              ],
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${widget.task.plannedMinutes}分钟',
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                  ),
                  const SizedBox(width: 16),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: SubjectColors.getCategoryColor(
                        widget.task.category,
                      ),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      widget.task.category,
                      style: const TextStyle(
                        fontSize: 11,
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Icon(
                    Icons.monetization_on,
                    size: 16,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '¥${widget.task.calculateWage().toStringAsFixed(0)}',
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                  ),
                  const Spacer(),
                  // 根据任务状态显示开始按钮或状态标签
                  if (widget.onStartTimer != null &&
                      (widget.task.status == TaskStatus.pending ||
                          widget.task.status == TaskStatus.inProgress))
                    ElevatedButton(
                      onPressed: () {
                        print('🔘 按钮被点击: ${widget.task.title}');
                        print('🔘 任务状态: ${widget.task.status}');
                        print(
                          '🔘 onStartTimer 是否为空: ${widget.onStartTimer == null}',
                        );
                        widget.onStartTimer!(widget.task);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF2E7EED),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        minimumSize: const Size(60, 28),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      child: Text(
                        widget.task.status == TaskStatus.pending ? '开始' : '继续',
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    )
                  else
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: widget.task.status.color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        widget.task.status.displayName,
                        style: TextStyle(
                          fontSize: 12,
                          color: widget.task.status.color,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// 任务编辑对话框
class TaskEditDialog extends StatefulWidget {
  final TimeBoxTask task;
  final Function(TimeBoxTask) onTaskUpdated;

  const TaskEditDialog({
    super.key,
    required this.task,
    required this.onTaskUpdated,
  });

  @override
  State<TaskEditDialog> createState() => _TaskEditDialogState();
}

class _TaskEditDialogState extends State<TaskEditDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;
  late TextEditingController _minutesController;
  final TaskCategoryManager _categoryManager = taskCategoryManager;

  late TaskPriority _priority;
  late String _category;
  // bool _isLoading = true; // 暂未使用

  /// 获取优先级对应的颜色
  Color _getPriorityColor(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.high:
        return const Color(0xFFE03E3E); // 红色
      case TaskPriority.medium:
        return const Color(0xFFFFD700); // 黄色
      case TaskPriority.low:
        return const Color(0xFF0F7B6C); // 绿色
    }
  }

  /// 获取优先级对应的图标
  IconData _getPriorityIcon(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.high:
        return Icons.priority_high;
      case TaskPriority.medium:
        return Icons.remove;
      case TaskPriority.low:
        return Icons.low_priority;
    }
  }

  /// 获取分类对应的图标
  IconData _getCategoryIcon(String category) {
    switch (category) {
      case '计算机科学':
        return Icons.computer;
      case '数学':
        return Icons.calculate;
      case '英语':
        return Icons.language;
      case '政治':
        return Icons.account_balance;
      default:
        return Icons.category;
    }
  }

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.task.title);
    _descriptionController = TextEditingController(
      text: widget.task.description,
    );
    _minutesController = TextEditingController(
      text: widget.task.plannedMinutes.toString(),
    );
    _priority = widget.task.priority;
    _category = widget.task.category;
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    await _categoryManager.loadFromStorage();
    if (mounted) {
      setState(() {
        // _isLoading = false; // 暂未使用
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 4,
      shadowColor: Colors.black.withValues(alpha: 0.1),
      title: const Text(
        '编辑时间盒子',
        style: TextStyle(
          color: Color(0xFF37352F),
          fontWeight: FontWeight.w600,
          fontSize: 18,
        ),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: '任务标题',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '请输入任务标题';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: '任务描述（可选）',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _minutesController,
                decoration: const InputDecoration(
                  labelText: '预计时长（分钟）',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '请输入预计时长';
                  }
                  if (int.tryParse(value) == null || int.parse(value) <= 0) {
                    return '请输入有效的时长';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // 优先级选择器
              DropdownButtonFormField<TaskPriority>(
                value: _priority,
                decoration: const InputDecoration(
                  labelText: '优先级',
                  border: OutlineInputBorder(),
                ),
                dropdownColor: Colors.white,
                items: TaskPriority.values.map((priority) {
                  final priorityColor = _getPriorityColor(priority);
                  final priorityIcon = _getPriorityIcon(priority);
                  return DropdownMenuItem(
                    value: priority,
                    child: Row(
                      children: [
                        Icon(priorityIcon, size: 16, color: priorityColor),
                        const SizedBox(width: 8),
                        Text(priority.displayName),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _priority = value!;
                  });
                },
              ),
              const SizedBox(height: 16),

              // 任务分类选择器
              Row(
                children: [
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _category,
                      decoration: const InputDecoration(
                        labelText: '任务分类',
                        border: OutlineInputBorder(),
                      ),
                      dropdownColor: Colors.white,
                      items: _categoryManager.categories.map((category) {
                        final categoryColor = _categoryManager.getCategoryColor(
                          category.name,
                        );
                        final categoryIcon = _getCategoryIcon(category.name);
                        return DropdownMenuItem(
                          value: category.name,
                          child: Row(
                            children: [
                              CircleAvatar(
                                backgroundColor: categoryColor,
                                radius: 6,
                              ),
                              const SizedBox(width: 8),
                              Icon(
                                categoryIcon,
                                size: 16,
                                color: categoryColor,
                              ),
                              const SizedBox(width: 8),
                              Text(category.name),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _category = value!;
                        });
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.settings),
                    onPressed: () {
                      Navigator.of(context)
                          .push(
                            MaterialPageRoute(
                              builder: (context) =>
                                  const TaskCategoryManagementPage(),
                            ),
                          )
                          .then((_) {
                            // 重新加载分类
                            _loadCategories();
                          });
                    },
                    tooltip: '管理分类',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      actionsPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      actions: [
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                '取消',
                style: TextStyle(color: Color(0xFF9B9A97)),
              ),
            ),
            const SizedBox(width: 12),
            ElevatedButton(
              onPressed: () {
                if (_formKey.currentState!.validate()) {
                  final updatedTask = widget.task.copyWith(
                    title: _titleController.text.trim(),
                    description: _descriptionController.text.trim(),
                    plannedMinutes: int.parse(_minutesController.text),
                    priority: _priority,
                    category: _category,
                  );
                  widget.onTaskUpdated(updatedTask);
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(
                    context,
                  ).showSnackBar(const SnackBar(content: Text('任务已更新')));
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2E7EED),
                foregroundColor: Colors.white,
                minimumSize: const Size(80, 48),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('保存'),
            ),
          ],
        ),
      ],
    );
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _minutesController.dispose();
    super.dispose();
  }
}
