import 'dart:io';
import 'dart:ui';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../utils/safe_area_helper.dart';
import '../../utils/image_rendering_fix.dart';

import '../../debug/photo_selection_advanced_diagnostics.dart';

/// 照片选择顺序管理器
/// 用于跟踪照片的选择顺序并提供数字指示器功能
class PhotoSelectionOrderManager {
  // 存储选择顺序的列表，按选择时间排序
  final List<String> _selectionOrder = [];

  /// 获取所有已选择的照片ID列表（按选择顺序）
  List<String> get selectedIds => List.unmodifiable(_selectionOrder);

  /// 获取已选择照片的数量
  int get selectedCount => _selectionOrder.length;

  /// 检查照片是否已被选择
  bool isSelected(String photoId) => _selectionOrder.contains(photoId);

  /// 获取照片的选择序号（从1开始），如果未选择返回null
  int? getSelectionOrder(String photoId) {
    final index = _selectionOrder.indexOf(photoId);
    return index >= 0 ? index + 1 : null;
  }

  /// 切换照片的选择状态
  /// 返回true表示照片被选中，false表示照片被取消选择
  bool toggleSelection(String photoId) {
    if (_selectionOrder.contains(photoId)) {
      // 取消选择：从列表中移除
      _selectionOrder.remove(photoId);
      return false;
    } else {
      // 选择：添加到列表末尾
      _selectionOrder.add(photoId);
      return true;
    }
  }

  /// 清空所有选择
  void clearAll() {
    _selectionOrder.clear();
  }

  /// 获取选择的照片ID集合（用于兼容现有代码）
  Set<String> get selectedIdsSet => _selectionOrder.toSet();
}

/// 照片选择数字指示器组件
/// 在照片右上角显示选择顺序的数字
class PhotoSelectionIndicator extends StatelessWidget {
  final int? selectionOrder; // 选择序号，null表示未选择
  final bool isSelected; // 是否已选择
  final double size; // 指示器大小

  const PhotoSelectionIndicator({
    super.key,
    required this.selectionOrder,
    required this.isSelected,
    this.size = 24.0,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isSelected
            ? const Color(0xFF2E7EED) // 选中状态：蓝色背景
            : Colors.white.withValues(alpha: 0.8), // 未选中状态：半透明白色背景
        border: isSelected ? null : Border.all(color: Colors.white, width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: isSelected && selectionOrder != null
            ? Text(
                '$selectionOrder',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              )
            : Icon(
                Icons.circle_outlined,
                size: size * 0.6,
                color: const Color(0xFF9B9A97),
              ),
      ),
    );
  }
}

/// 照片相册数据模型
class PhotoAlbum {
  final String id;
  final String title;
  final List<String> imagePaths;
  final DateTime createdAt;
  final DateTime lastModified;
  final String? description;
  final String? category; // 新增分类字段

  PhotoAlbum({
    required this.id,
    required this.title,
    required this.imagePaths,
    required this.createdAt,
    required this.lastModified,
    this.description,
    this.category, // 新增分类参数
  });

  /// 获取封面图片路径（第一张图片）
  String get coverImagePath => imagePaths.isNotEmpty ? imagePaths.first : '';

  /// 获取图片数量
  int get imageCount => imagePaths.length;

  /// 复制并更新图片列表
  PhotoAlbum copyWith({
    String? title,
    List<String>? imagePaths,
    DateTime? lastModified,
    String? description,
    String? category,
  }) {
    return PhotoAlbum(
      id: id,
      title: title ?? this.title,
      imagePaths: imagePaths ?? this.imagePaths,
      createdAt: createdAt,
      lastModified: lastModified ?? DateTime.now(),
      description: description ?? this.description,
      category: category ?? this.category,
    );
  }
}

/// 照片相册创建页面
class PhotoAlbumCreatorPage extends StatefulWidget {
  const PhotoAlbumCreatorPage({super.key});

  @override
  State<PhotoAlbumCreatorPage> createState() => _PhotoAlbumCreatorPageState();
}

class _PhotoAlbumCreatorPageState extends State<PhotoAlbumCreatorPage>
    with TickerProviderStateMixin {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final ImagePicker _imagePicker = ImagePicker();

  final List<String> _selectedImagePaths = [];
  bool _isLoading = false;
  String? _errorMessage;
  bool _isSimulator = false;
  bool _hasCheckedSimulator = false;

  // 动画控制器
  late AnimationController _fadeAnimationController;
  late AnimationController _slideAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _checkEnvironment();
  }

  /// 检测运行环境（应急简化版本）
  Future<void> _checkEnvironment() async {
    print('🔍 [应急模式] 简化环境检测');

    // 🚨 应急模式：跳过复杂检测，直接启用所有调试功能
    _isSimulator = true; // 强制启用调试工具
    _hasCheckedSimulator = true;

    print('🔍 [应急模式] 强制启用调试模式');
    print('   - Platform.isIOS: ${Platform.isIOS}');
    print('   - 当前平台: ${_getPlatformName()}');

    if (mounted) {
      setState(() {});
    }
  }

  /// 获取平台名称
  String _getPlatformName() {
    if (Platform.isIOS) return 'iOS';
    if (Platform.isAndroid) return 'Android';
    if (Platform.isMacOS) return 'macOS';
    if (Platform.isWindows) return 'Windows';
    if (Platform.isLinux) return 'Linux';
    return 'Unknown';
  }

  /// 获取环境提示信息
  String _getEnvironmentMessage() {
    final platformName = _getPlatformName();

    // 🚨 应急模式提示
    return '🚨 应急模式已启用 - $platformName平台\n'
        '使用超级简化的照片选择流程，带10秒超时保护。\n'
        '此模式避免复杂逻辑可能导致的阻塞问题。';
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _fadeAnimationController.dispose();
    _slideAnimationController.dispose();
    super.dispose();
  }

  /// 初始化动画
  void _initializeAnimations() {
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _fadeAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _slideAnimationController,
            curve: Curves.easeOutCubic,
          ),
        );
  }

  /// 显示高级诊断工具
  void _showAdvancedDiagnostics() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const PhotoSelectionAdvancedDiagnostics(),
      ),
    );
  }

  /// 检查并请求照片权限
  Future<bool> _checkAndRequestPhotoPermission() async {
    try {
      print('🔐 开始检查照片权限...');
      print('🔐 Platform.isAndroid: ${Platform.isAndroid}');
      print('🔐 Platform.isIOS: ${Platform.isIOS}');

      // 1. Android特殊处理：分别尝试不同的权限
      if (Platform.isAndroid) {
        print('🔐 Android平台：使用专门的权限检查逻辑');
        return await _checkAndroidPhotoPermissions();
      }

      // 2. iOS和其他平台：使用PhotoManager
      final currentPermission = await PhotoManager.requestPermissionExtend();

      print('🔐 权限状态: ${currentPermission.name}');
      print('🔐 isAuth: ${currentPermission.isAuth}');

      // 3. 直接检查 isAuth 状态
      if (currentPermission.isAuth) {
        print('✅ 权限已授予，可以访问相册');
        return true;
      }

      // 4. 检查 limited 权限（iOS 14+ 部分访问）
      if (currentPermission == PermissionState.limited) {
        print('⚠️ iOS 14+ 部分访问权限，仍可使用');
        return true;
      }

      // 5. 权限未授予的情况
      print('❌ 权限未授予: ${currentPermission.name}');

      // 根据权限状态显示不同的引导
      if (currentPermission == PermissionState.denied ||
          currentPermission == PermissionState.notDetermined) {
        print('显示权限请求引导对话框');
        return await _showPermissionGuidanceDialog();
      } else if (currentPermission == PermissionState.restricted) {
        print('显示设置页面引导');
        return await _showPermissionSettingsDialog();
      } else {
        print('显示通用权限错误');
        _showSnackBar('需要相册访问权限才能选择照片', isError: true);
        return false;
      }
    } catch (e, stackTrace) {
      print('❌ 权限检查异常: $e');
      print('Stack trace: $stackTrace');

      // 异常情况下，尝试简单的权限检查
      try {
        final simpleCheck = await PhotoManager.requestPermissionExtend();
        print(
          '🔄 简单权限检查结果: ${simpleCheck.name}, isAuth: ${simpleCheck.isAuth}',
        );

        if (simpleCheck.isAuth || simpleCheck == PermissionState.limited) {
          return true;
        }
      } catch (e2) {
        print('❌ 简单权限检查也失败: $e2');
      }

      _showSnackBar('权限检查失败，请检查应用权限设置', isError: true);
      return false;
    }
  }

  /// Android专门的权限检查和请求逻辑
  Future<bool> _checkAndroidPhotoPermissions() async {
    try {
      print('🔐 [Android] 开始Android权限检查...');

      // 1. 优先检查permission_handler权限状态（更可靠）
      bool systemPermissionGranted = false;

      // 先尝试photos权限（Android 13+）
      try {
        print('🔐 [Android] 检查Permission.photos...');
        var photosStatus = await Permission.photos.status;
        print('🔐 [Android] Permission.photos状态: ${photosStatus.name}');

        if (photosStatus.isGranted) {
          print('✅ [Android] Permission.photos权限已获得');
          systemPermissionGranted = true;
        } else if (photosStatus.isDenied) {
          print('🔐 [Android] 请求Permission.photos权限...');
          var photosResult = await Permission.photos.request();
          print('🔐 [Android] Permission.photos请求结果: ${photosResult.name}');

          if (photosResult.isGranted) {
            print('✅ [Android] Permission.photos权限请求成功');
            systemPermissionGranted = true;
          }
        }
      } catch (e) {
        print('⚠️ [Android] Permission.photos处理失败: $e');
      }

      // 如果photos权限未获得，尝试storage权限（Android 12及以下）
      if (!systemPermissionGranted) {
        try {
          print('🔐 [Android] 检查Permission.storage...');
          var storageStatus = await Permission.storage.status;
          print('🔐 [Android] Permission.storage状态: ${storageStatus.name}');

          if (storageStatus.isGranted) {
            print('✅ [Android] Permission.storage权限已获得');
            systemPermissionGranted = true;
          } else if (storageStatus.isDenied) {
            print('🔐 [Android] 请求Permission.storage权限...');
            var storageResult = await Permission.storage.request();
            print('🔐 [Android] Permission.storage请求结果: ${storageResult.name}');

            if (storageResult.isGranted) {
              print('✅ [Android] Permission.storage权限请求成功');
              systemPermissionGranted = true;
            } else if (storageResult.isPermanentlyDenied) {
              print('⚠️ [Android] 存储权限被永久拒绝');
              return await _showPermissionSettingsDialog();
            }
          }
        } catch (e) {
          print('⚠️ [Android] Permission.storage处理失败: $e');
        }
      }

      // 2. 如果系统权限已获得，尝试实际访问照片来验证
      if (systemPermissionGranted) {
        try {
          print('🔐 [Android] 系统权限已获得，验证实际照片访问能力...');

          // 尝试获取照片列表来验证权限
          final List<AssetPathEntity> albums =
              await PhotoManager.getAssetPathList(
                type: RequestType.image,
                hasAll: true,
              );

          if (albums.isNotEmpty) {
            print('✅ [Android] 验证成功：可以访问${albums.length}个相册');

            // 尝试获取第一张照片
            final firstAlbum = albums.first;
            final assetCount = await firstAlbum.assetCountAsync;
            print('✅ [Android] 验证成功：第一个相册有$assetCount张照片');

            if (assetCount > 0) {
              final assets = await firstAlbum.getAssetListPaged(
                page: 0,
                size: 1,
              );
              if (assets.isNotEmpty) {
                print('✅ [Android] 验证成功：可以获取具体照片数据');
                return true;
              }
            } else {
              print('✅ [Android] 验证成功：相册为空但权限正常');
              return true;
            }
          } else {
            print('⚠️ [Android] 权限已授予但无法获取相册列表');
          }
        } catch (e) {
          print('⚠️ [Android] 照片访问验证失败: $e');
          // 即使验证失败，如果系统权限已授予，也允许继续
          print('🔧 [Android] 系统权限已授予，允许继续尝试');
          return true;
        }
      }

      // 3. 最后尝试PhotoManager权限（作为补充）
      try {
        print('🔐 [Android] 尝试PhotoManager权限...');
        final pmPermission = await PhotoManager.requestPermissionExtend();
        print(
          '🔐 [Android] PhotoManager权限: ${pmPermission.name}, isAuth: ${pmPermission.isAuth}',
        );

        if (pmPermission.isAuth || pmPermission == PermissionState.limited) {
          print('✅ [Android] PhotoManager权限获得');
          return true;
        }
      } catch (e) {
        print('⚠️ [Android] PhotoManager权限检查失败: $e');
      }

      // 4. 如果有系统权限但其他验证失败，给用户一个尝试的机会
      if (systemPermissionGranted) {
        print('🔧 [Android] 系统权限已授予，强制允许访问');
        return true;
      }

      // 5. 所有权限尝试都失败
      print('❌ [Android] 所有权限尝试都失败');
      _showSnackBar('无法获取相册权限，请在设置中手动授权', isError: true);
      return false;
    } catch (e) {
      print('❌ [Android] Android权限检查异常: $e');
      _showSnackBar('权限检查异常，请在设置中检查权限状态', isError: true);
      return false;
    }
  }

  /// 显示权限引导对话框
  Future<bool> _showPermissionGuidanceDialog() async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('需要相册权限'),
        content: const Text(
          '为了让您选择照片创建相册，需要访问您的相册。\n\n'
          '请点击"授权"按钮，在弹出的系统对话框中选择"允许"。',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2E7EED),
            ),
            child: const Text('授权', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (result == true) {
      // 用户同意，再次请求权限
      final permission = await PhotoManager.requestPermissionExtend();
      return permission.isAuth;
    }

    return false;
  }

  /// 显示设置页面引导对话框
  Future<bool> _showPermissionSettingsDialog() async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('需要在设置中开启权限'),
        content: Text(
          Platform.isIOS
              ? '相册权限被限制。\n\n'
                    '请前往 iPhone设置 → OneDay → 照片，\n'
                    '选择"所有照片"或"选中的照片"。'
              : '相册权限被限制。\n\n'
                    '请前往 设置 → 应用管理 → OneDay → 权限管理，\n'
                    '开启"存储"或"照片"权限。',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop(true);
              // 尝试打开应用设置页面
              await PhotoManager.openSetting();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2E7EED),
            ),
            child: const Text('去设置', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    return result == true;
  }

  /// 显示图片选择选项（方案A：强制使用自定义/半屏选择器）
  Future<void> _showImagePickerOptions() async {
    print('📸 === 照片选择流程开始（方案A：强制自定义选择器） ===');
    print('   - 当前平台: ${_getPlatformName()}');

    // 方案A：不再提供系统选择器选项，直接进入自定义/半屏选择器
    try {
      await _openDouyinStylePicker(); // 半屏，自带 SafeArea，状态栏可见
    } catch (e) {
      print('⚠️ 半屏选择器打开失败，回退到全屏自定义选择器: $e');
      await _openCustomPhotoSelector();
    }
  }

  /// 打开自定义照片选择器
  Future<void> _openCustomPhotoSelector() async {
    try {
      // 检查并请求照片权限
      final hasPermission = await _checkAndRequestPhotoPermission();
      if (!hasPermission) {
        return;
      }

      // 打开自定义照片选择器
      if (!mounted) return;
      final result = await Navigator.of(context).push<List<String>>(
        MaterialPageRoute(
          builder: (context) => const CustomPhotoSelectorPage(),
        ),
      );

      if (result != null && result.isNotEmpty && mounted) {
        setState(() {
          _selectedImagePaths.addAll(result);
        });

        // 启动动画
        if (_selectedImagePaths.length == result.length) {
          _fadeAnimationController.forward();
          _slideAnimationController.forward();
        }

        HapticFeedback.lightImpact();
        _showSnackBar('✅ 成功选择 ${result.length} 张照片', isError: false);
      }
    } catch (e) {
      print('❌ 自定义照片选择失败: $e');
      _showSnackBar('照片选择失败：${e.toString()}', isError: true);
    }
  }

  /// 打开抖音风格底部弹窗选择器
  Future<void> _openDouyinStylePicker() async {
    try {
      // 检查并请求照片权限
      final hasPermission = await _checkAndRequestPhotoPermission();
      if (!hasPermission) {
        return;
      }

      if (!mounted) return;
      final result = await showModalBottomSheet<List<String>>(
        context: context,
        isScrollControlled: true,
        useSafeArea: true,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return const _DouyinPhotoPickerSheet();
        },
      );

      if (result != null && result.isNotEmpty && mounted) {
        setState(() => _selectedImagePaths.addAll(result));
        if (_selectedImagePaths.length == result.length) {
          _fadeAnimationController.forward();
          _slideAnimationController.forward();
        }
        HapticFeedback.lightImpact();
        _showSnackBar('✅ 已选择 ${result.length} 张照片', isError: false);
      }
    } catch (e) {
      _showSnackBar('选择器打开失败：$e');
    }
  }

  /// 应急照片选择（使用SafeAreaHelper避免层级冲突）
  Future<void> _emergencyPhotoSelection() async {
    print('🚨 [应急模式] 启动防阻塞照片选择');

    try {
      if (mounted) {
        setState(() {
          _isLoading = true;
          _errorMessage = null;
        });
      }

      // 🔧 使用SafeAreaHelper.showSystemDialogSafely来安全调用照片选择器
      // 这个方法会自动处理状态栏冲突和层级问题
      print('🔧 [应急模式] 使用SafeAreaHelper安全调用照片选择器');

      final XFile? image = await SafeAreaHelper.showSystemDialogSafely<XFile?>(
        context,
        () async {
          return await _imagePicker
              .pickImage(source: ImageSource.gallery, imageQuality: 90)
              .timeout(
                const Duration(seconds: 10),
                onTimeout: () {
                  print('⏰ [应急模式] 照片选择超时');
                  return null;
                },
              );
        },
        // 关键修复：不再绕过包装器，让 SafeAreaHelper 统一管理状态栏显示与样式
        bypassWrapper: false,
      );

      if (image != null) {
        print('✅ [应急模式] 照片选择成功: ${image.path}');
        // 🚨 简化图片处理：直接添加，不压缩
        if (mounted) {
          setState(() {
            _selectedImagePaths.add(image.path);
          });

          // 启动动画
          if (_selectedImagePaths.length == 1) {
            _fadeAnimationController.forward();
            _slideAnimationController.forward();
          }

          HapticFeedback.lightImpact();
          _showSnackBar('✅ 应急模式：成功选择1张照片', isError: false);
        }
      } else {
        print('📸 [应急模式] 用户取消或超时');
        _showSnackBar('照片选择被取消或超时', isError: false);
      }
    } catch (e) {
      print('❌ [应急模式] 照片选择失败: $e');
      if (mounted) {
        _showSnackBar('应急模式选择失败：${e.toString()}', isError: true);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 最基础的照片选择（去掉所有优化，用于调试）
  Future<void> _tryBasicPhotoSelection() async {
    print('📸 [基础模式] 启动基础照片选择');
    await _emergencyPhotoSelection(); // 使用应急模式
  }

  /// 显示灵动岛UI测试指导
  void _showDynamicIslandTest() {
    final deviceInfo = SafeAreaHelper.getDeviceInfo(context);
    final hasDynamicIsland = deviceInfo['hasDynamicIsland'] == true;
    final hasNotch = deviceInfo['hasNotch'] == true;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.phone_iphone_outlined, color: Color(0xFF2E7EED)),
            SizedBox(width: 8),
            Text('设备UI适配测试'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '当前设备信息：',
                style: const TextStyle(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              Text('• 设备类型: ${deviceInfo['deviceType']}'),
              Text('• 顶部安全区域: ${deviceInfo['topSafeArea']}pt'),
              Text('• 是否有灵动岛: ${hasDynamicIsland ? "是" : "否"}'),
              Text('• 是否有刘海屏: ${hasNotch ? "是" : "否"}'),

              const SizedBox(height: 16),
              const Text(
                '照片选择器UI检查清单：',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              const Text('□ 顶部"Cancel"按钮是否清晰可见'),
              const Text('□ 顶部"Add"按钮是否清晰可见'),
              const Text('□ "Private Access to Photos"提示框是否完整显示'),
              const Text('□ 照片网格是否正常显示'),
              const Text('□ 底部"Select Photos"按钮是否可见'),

              if (hasDynamicIsland) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    '💡 灵动岛设备特别提示：\n'
                    '• 顶部控件应该自动向下移动避开灵动岛\n'
                    '• 如果仍被遮挡，请尝试重启应用或模拟器',
                    style: TextStyle(fontSize: 12),
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showImagePickerOptions(); // 测试照片选择器
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2E7EED),
            ),
            child: const Text('测试照片选择器', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  /// 清空所有图片
  Future<void> _clearAllImages() async {
    final bool? confirmed = await _showConfirmDialog(
      title: '清空全部图片',
      content: '确定要清空所有已选择的图片吗？此操作无法撤销。',
      confirmText: '清空',
      cancelText: '取消',
    );

    if (confirmed == true) {
      setState(() {
        _selectedImagePaths.clear();
      });

      // 重置动画
      _fadeAnimationController.reset();
      _slideAnimationController.reset();

      // 触觉反馈
      HapticFeedback.mediumImpact();

      _showSnackBar('已清空所有图片', isError: false);
    }
  }

  /// 移除单张图片
  void _removeImage(int index) {
    setState(() {
      _selectedImagePaths.removeAt(index);
    });

    // 如果没有图片了，重置动画
    if (_selectedImagePaths.isEmpty) {
      _fadeAnimationController.reset();
      _slideAnimationController.reset();
    }

    HapticFeedback.lightImpact();
  }

  /// 重新排序图片
  void _reorderImages(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final String item = _selectedImagePaths.removeAt(oldIndex);
      _selectedImagePaths.insert(newIndex, item);
    });

    HapticFeedback.selectionClick();
  }

  /// 显示确认对话框
  Future<bool?> _showConfirmDialog({
    required String title,
    required String content,
    required String confirmText,
    required String cancelText,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(cancelText),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFEB5757), // 红色警告按钮
            ),
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }

  /// 显示提示消息
  void _showSnackBar(String message, {bool isError = true}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError
            ? const Color(0xFFEB5757)
            : const Color(0xFF4CAF50),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// 创建相册
  Future<void> _createAlbum() async {
    final String title = _titleController.text.trim();

    if (title.isEmpty) {
      _showSnackBar('请输入相册标题');
      return;
    }

    if (_selectedImagePaths.isEmpty) {
      _showSnackBar('请至少选择一张图片');
      return;
    }

    try {
      setState(() {
        _isLoading = true;
      });

      final album = PhotoAlbum(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: title,
        imagePaths: List.from(_selectedImagePaths),
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
      );

      // 这里可以保存到数据库或传递给父组件
      // 直接返回相册对象，不显示成功提示（相册已经在列表中显示了）
      if (mounted) {
        Navigator.of(context).pop(album);
      }
    } catch (e) {
      _showSnackBar('创建相册失败：${e.toString()}');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 修复布局问题：使用SafeAreaScaffold确保所有内容显示在安全区域内
    // 避免UI元素遮挡顶部系统状态栏
    return SafeAreaScaffold(
      title: '创建知忆相册',
      backgroundColor: Colors.white,
      appBarBackgroundColor: Colors.white,
      appBarForegroundColor: const Color(0xFF37352F),
      optimizeForPhotoSelection: true, // 启用照片选择优化，防止状态栏遮挡
      actions: [
        // iOS模拟器环境下显示诊断按钮
        if (_isSimulator && _hasCheckedSimulator)
          PopupMenuButton<String>(
            icon: const Icon(Icons.bug_report_outlined, size: 20),
            tooltip: '照片选择工具',
            onSelected: (value) {
              switch (value) {
                case 'diagnostics':
                  _showAdvancedDiagnostics();
                  break;
                case 'basic_test':
                  _tryBasicPhotoSelection();
                  break;
                case 'ui_test':
                  _showDynamicIslandTest();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'diagnostics',
                child: Row(
                  children: [
                    Icon(Icons.build_outlined, size: 18),
                    SizedBox(width: 8),
                    Text('高级诊断'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'basic_test',
                child: Row(
                  children: [
                    Icon(Icons.photo_outlined, size: 18),
                    SizedBox(width: 8),
                    Text('基础照片选择'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'ui_test',
                child: Row(
                  children: [
                    Icon(Icons.phone_iphone_outlined, size: 18),
                    SizedBox(width: 8),
                    Text('灵动岛测试'),
                  ],
                ),
              ),
            ],
          ),
        if (_selectedImagePaths.isNotEmpty)
          TextButton(
            onPressed: _isLoading ? null : _createAlbum,
            child: _isLoading
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Color(0xFF2E7EED),
                      ),
                    ),
                  )
                : const Text(
                    '创建',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF2E7EED),
                    ),
                  ),
          ),
      ],
      body: _buildBody(),
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 🚨 应急模式环境信息显示
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: const Color(0xFF2E7EED).withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.info_outline,
                  color: Color(0xFF2E7EED),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _getEnvironmentMessage(),
                    style: const TextStyle(
                      color: Color(0xFF2E7EED),
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // 相册信息输入区域
          _buildAlbumInfoSection(),

          const SizedBox(height: 24),

          // 图片选择按钮
          _buildImagePickerButton(),

          const SizedBox(height: 16),

          // 权限调试按钮
          _buildPermissionDebugButton(),

          const SizedBox(height: 16),

          // 环境检测提示
          if (_hasCheckedSimulator) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: const Color(0xFF2E7EED).withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.info_outline,
                    color: Color(0xFF2E7EED),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _getEnvironmentMessage(),
                      style: const TextStyle(
                        color: Color(0xFF2E7EED),
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          // 错误消息显示
          if (_errorMessage != null) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFFEB5757).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: const Color(0xFFEB5757).withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Color(0xFFEB5757),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _errorMessage!,
                      style: const TextStyle(
                        color: Color(0xFFEB5757),
                        fontSize: 14,
                      ),
                    ),
                  ),
                  // 如果是模拟器环境，添加快速诊断按钮
                  if (_isSimulator)
                    IconButton(
                      onPressed: _showAdvancedDiagnostics,
                      icon: const Icon(Icons.build_outlined),
                      iconSize: 18,
                      tooltip: '诊断工具',
                      color: const Color(0xFFEB5757),
                    ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          // 图片预览区域
          if (_selectedImagePaths.isNotEmpty) ...[
            FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: _buildImagePreviewSection(),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建相册信息输入区域
  Widget _buildAlbumInfoSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFFAFAFA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF37352F).withValues(alpha: 0.08),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '相册信息',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 16),

          // 相册标题输入
          TextField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: '相册标题',
              hintText: '请输入相册标题',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8)),
                borderSide: BorderSide(color: Color(0xFFE3E2E0)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8)),
                borderSide: BorderSide(color: Color(0xFF2E7EED), width: 2),
              ),
              labelStyle: TextStyle(color: Color(0xFF6E6E6E)),
            ),
            style: const TextStyle(fontSize: 16, color: Color(0xFF37352F)),
            maxLength: 50,
            textInputAction: TextInputAction.next,
          ),

          const SizedBox(height: 16),

          // 相册描述输入
          TextField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: '相册描述（可选）',
              hintText: '请输入相册描述',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8)),
                borderSide: BorderSide(color: Color(0xFFE3E2E0)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8)),
                borderSide: BorderSide(color: Color(0xFF2E7EED), width: 2),
              ),
              labelStyle: TextStyle(color: Color(0xFF6E6E6E)),
            ),
            style: const TextStyle(fontSize: 16, color: Color(0xFF37352F)),
            maxLines: 3,
            maxLength: 200,
            textInputAction: TextInputAction.done,
          ),
        ],
      ),
    );
  }

  /// 构建图片选择按钮
  Widget _buildImagePickerButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton.icon(
        // 使用抖音风格半屏选择器，永不遮挡状态栏
        onPressed: _isLoading ? null : _openDouyinStylePicker,
        icon: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Icon(Icons.add_photo_alternate_outlined),
        label: Text(
          _selectedImagePaths.isEmpty ? '从相册选择图片' : '添加更多图片',
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF2E7EED),
          foregroundColor: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        ),
      ),
    );
  }

  /// 权限调试按钮
  Widget _buildPermissionDebugButton() {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: OutlinedButton.icon(
        onPressed: _showPermissionDebugDialog,
        icon: const Icon(Icons.bug_report, size: 20),
        label: const Text(
          '🔍 权限状态调试',
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        ),
        style: OutlinedButton.styleFrom(
          foregroundColor: const Color(0xFF2E7EED),
          side: const BorderSide(color: Color(0xFF2E7EED)),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
    );
  }

  /// 显示权限调试对话框
  Future<void> _showPermissionDebugDialog() async {
    String debugInfo = '正在检查权限状态...\n\n';

    try {
      // 检查当前权限状态
      debugInfo += '=== PhotoManager 权限检查 ===\n';
      final permission = await PhotoManager.requestPermissionExtend();
      debugInfo += '权限状态: ${permission.name}\n';
      debugInfo += 'isAuth: ${permission.isAuth}\n';
      debugInfo += '平台: ${Platform.isIOS ? 'iOS' : 'Android'}\n\n';

      // 尝试获取相册列表来验证权限
      debugInfo += '=== 相册访问测试 ===\n';
      try {
        final albums = await PhotoManager.getAssetPathList(
          type: RequestType.image,
          hasAll: true,
        );
        debugInfo += '成功获取到 ${albums.length} 个相册\n';
        if (albums.isNotEmpty) {
          debugInfo += '第一个相册: ${albums.first.name}\n';

          // 尝试获取第一张图片
          final assets = await albums.first.getAssetListPaged(page: 0, size: 1);
          debugInfo += '第一个相册中有 ${assets.length} 张图片\n';
        }
      } catch (e) {
        debugInfo += '❌ 获取相册失败: $e\n';
      }
    } catch (e) {
      debugInfo += '❌ 权限检查失败: $e\n';
    }

    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('权限调试信息'),
          content: SingleChildScrollView(
            child: Text(
              debugInfo,
              style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('关闭'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // 重新尝试权限检查
                _checkAndRequestPhotoPermission();
              },
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }
  }

  /// 构建图片预览区域
  Widget _buildImagePreviewSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFFAFAFA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF37352F).withValues(alpha: 0.08),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题和操作按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '已选择 ${_selectedImagePaths.length} 张图片',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF37352F),
                ),
              ),
              if (_selectedImagePaths.isNotEmpty)
                TextButton.icon(
                  onPressed: _clearAllImages,
                  icon: const Icon(
                    Icons.clear_all,
                    size: 18,
                    color: Color(0xFFEB5757),
                  ),
                  label: const Text(
                    '清空全部',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFFEB5757),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ),
            ],
          ),

          const SizedBox(height: 12),

          // 封面预览提示
          if (_selectedImagePaths.isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: const Color(0xFF2E7EED).withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.info_outline,
                    color: Color(0xFF2E7EED),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      '第一张图片将作为相册封面',
                      style: TextStyle(
                        color: Color(0xFF2E7EED),
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          // 图片预览列表
          _buildImagePreviewList(),

          const SizedBox(height: 16),

          // 从相册添加图片按钮（统一走抖音风格选择器）
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: _openDouyinStylePicker,
              icon: const Icon(Icons.photo_library_outlined),
              label: const Text('从相册添加图片'),
              style: OutlinedButton.styleFrom(
                foregroundColor: const Color(0xFF2E7EED),
                side: const BorderSide(color: Color(0xFF2E7EED)),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建图片预览列表（支持拖拽排序和响应式设计）
  Widget _buildImagePreviewList() {
    if (_selectedImagePaths.isEmpty) {
      return const SizedBox.shrink();
    }

    // 响应式设计：根据屏幕宽度调整图片大小
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;
    final imageSize = isTablet ? 140.0 : 100.0;
    final spacing = isTablet ? 12.0 : 8.0;

    return SizedBox(
      height: imageSize + 20, // 额外空间用于拖拽效果
      child: ReorderableListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _selectedImagePaths.length,
        onReorder: _reorderImages,
        proxyDecorator: (child, index, animation) {
          return AnimatedBuilder(
            animation: animation,
            builder: (context, child) {
              final double animValue = Curves.easeInOut.transform(
                animation.value,
              );
              final double elevation = lerpDouble(0, 6, animValue)!;
              final double scale = lerpDouble(1, 1.05, animValue)!;

              return Transform.scale(
                scale: scale,
                child: Material(
                  elevation: elevation,
                  borderRadius: BorderRadius.circular(12),
                  child: child,
                ),
              );
            },
            child: child,
          );
        },
        itemBuilder: (context, index) {
          final imagePath = _selectedImagePaths[index];
          final isFirst = index == 0;

          return Container(
            key: ValueKey(imagePath),
            width: imageSize,
            margin: EdgeInsets.only(
              right: index < _selectedImagePaths.length - 1 ? spacing : 0,
            ),
            child: Stack(
              children: [
                // 图片容器
                Container(
                  width: imageSize,
                  height: imageSize,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    border: isFirst
                        ? Border.all(color: const Color(0xFF2E7EED), width: 2)
                        : Border.all(color: const Color(0xFFE3E2E0), width: 1),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: GestureDetector(
                      onTap: () => _showImagePreview(index),
                      child: ImageRenderingFix.buildOptimizedImageFile(
                        File(imagePath),
                        fit: BoxFit.cover,
                        errorWidget: Container(
                          color: const Color(0xFFF5F5F5),
                          child: Icon(
                            Icons.broken_image,
                            color: const Color(0xFF9B9A97),
                            size: imageSize * 0.3,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                // 封面标识
                if (isFirst)
                  Positioned(
                    top: 4,
                    left: 4,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFF2E7EED),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Text(
                        '封面',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),

                // 删除按钮
                Positioned(
                  top: 4,
                  right: 4,
                  child: GestureDetector(
                    onTap: () => _removeImage(index),
                    child: Container(
                      width: isTablet ? 28 : 24,
                      height: isTablet ? 28 : 24,
                      decoration: BoxDecoration(
                        color: const Color(0xFFEB5757),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.close,
                        color: Colors.white,
                        size: isTablet ? 18 : 16,
                      ),
                    ),
                  ),
                ),

                // 拖拽手柄
                Positioned(
                  bottom: 4,
                  right: 4,
                  child: Container(
                    width: isTablet ? 28 : 24,
                    height: isTablet ? 28 : 24,
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.6),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Icon(
                      Icons.drag_handle,
                      color: Colors.white,
                      size: isTablet ? 18 : 16,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 显示图片预览
  void _showImagePreview(int initialIndex) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _ImagePreviewPage(
          imagePaths: _selectedImagePaths,
          initialIndex: initialIndex,
        ),
      ),
    );
  }
}

/// 图片预览页面
class _ImagePreviewPage extends StatefulWidget {
  final List<String> imagePaths;
  final int initialIndex;

  const _ImagePreviewPage({
    required this.imagePaths,
    required this.initialIndex,
  });

  @override
  State<_ImagePreviewPage> createState() => _ImagePreviewPageState();
}

class _ImagePreviewPageState extends State<_ImagePreviewPage> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: Text(
          '${_currentIndex + 1} / ${widget.imagePaths.length}',
          style: const TextStyle(color: Colors.white),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: PageView.builder(
        controller: _pageController,
        itemCount: widget.imagePaths.length,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        itemBuilder: (context, index) {
          return InteractiveViewer(
            minScale: 0.5,
            maxScale: 3.0,
            child: Center(
              child: ImageRenderingFix.buildOptimizedImageFile(
                File(widget.imagePaths[index]),
                fit: BoxFit.contain,
                errorWidget: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.broken_image, color: Colors.white54, size: 64),
                      SizedBox(height: 16),
                      Text(
                        '图片加载失败',
                        style: TextStyle(color: Colors.white54, fontSize: 16),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

/// 自定义照片选择器页面
/// 修复问题：
/// 1. 使用 Scaffold + SafeArea 确保安全区域显示
/// 2. 实现 GridView 显示照片网格
/// 3. 添加状态管理 `Set<String> _selectedIds` 跟踪选中状态
/// 4. 实现点击选择功能和视觉反馈
/// 5. 添加 Cancel 和 Add 按钮，修复交互失效问题
class CustomPhotoSelectorPage extends StatefulWidget {
  const CustomPhotoSelectorPage({super.key});

  @override
  State<CustomPhotoSelectorPage> createState() =>
      _CustomPhotoSelectorPageState();
}

/// 抖音风格底部弹窗选择器
class _DouyinPhotoPickerSheet extends StatefulWidget {
  const _DouyinPhotoPickerSheet();

  @override
  State<_DouyinPhotoPickerSheet> createState() =>
      _DouyinPhotoPickerSheetState();
}

class _DouyinPhotoPickerSheetState extends State<_DouyinPhotoPickerSheet> {
  List<AssetEntity> _photos = [];
  final PhotoSelectionOrderManager _selectionManager =
      PhotoSelectionOrderManager();
  bool _loading = true;
  bool _confirming = false; // 新增：确认中状态

  @override
  void initState() {
    super.initState();
    _load();
  }

  Future<void> _load() async {
    final albums = await PhotoManager.getAssetPathList(
      type: RequestType.image,
      onlyAll: true,
    );
    if (albums.isNotEmpty) {
      final items = await albums.first.getAssetListPaged(page: 0, size: 1000);
      setState(() {
        _photos = items;
        _loading = false;
      });
    } else {
      setState(() => _loading = false);
    }
  }

  Future<Widget> _thumb(AssetEntity e) async {
    final data = await e.thumbnailDataWithSize(
      const ThumbnailSize(300, 300),
      quality: 85,
    );
    if (data == null) {
      return Container(color: const Color(0xFFF5F5F5));
    }
    return Image.memory(data, fit: BoxFit.cover);
  }

  Future<void> _confirm() async {
    if (_confirming) return; // 防止重复点击

    setState(() {
      _confirming = true;
    });

    try {
      print('📷 [DouyinPicker] 开始处理选中的 ${_selectionManager.selectedCount} 张照片');

      final List<String> paths = [];
      final selectedIds = _selectionManager.selectedIds;

      // 分批处理照片，避免阻塞UI（iPad优化）
      const batchSize = 3; // 每批处理3张照片
      for (int i = 0; i < selectedIds.length; i += batchSize) {
        final batch = selectedIds.skip(i).take(batchSize).toList();

        print(
          '📷 [DouyinPicker] 处理批次 ${(i / batchSize + 1).ceil()}/${(selectedIds.length / batchSize).ceil()}',
        );

        // 并行处理当前批次的照片
        final futures = batch.map((photoId) async {
          try {
            final photo = _photos.firstWhere((p) => p.id == photoId);
            final file = await photo.file;
            return file?.path;
          } catch (e) {
            print('⚠️ [DouyinPicker] 获取照片文件失败: $e');
            return null;
          }
        }).toList();

        final batchPaths = await Future.wait(futures);

        // 添加成功获取的路径
        for (final path in batchPaths) {
          if (path != null) {
            paths.add(path);
          }
        }

        // 给UI一点时间更新（iPad优化）
        if (i + batchSize < selectedIds.length) {
          await Future.delayed(const Duration(milliseconds: 50));
        }
      }

      print('✅ [DouyinPicker] 成功处理 ${paths.length}/${selectedIds.length} 张照片');

      if (mounted) {
        Navigator.of(context).pop(paths);
      }
    } catch (e) {
      print('❌ [DouyinPicker] 照片处理失败: $e');

      if (mounted) {
        setState(() {
          _confirming = false;
        });

        // 显示错误提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('处理照片失败: $e'),
            backgroundColor: const Color(0xFFEB5757),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final mq = MediaQuery.of(context);
    final height = mq.size.height * 0.86; // 半屏~0.86，顶部始终留出状态栏空间

    return SafeArea(
      top: true,
      child: Container(
        height: height,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Stack(
          children: [
            Column(
              children: [
                // 顶部拖拽条 + 操作栏
                Padding(
                  padding: const EdgeInsets.only(top: 8, bottom: 8),
                  child: Container(
                    width: 36,
                    height: 4,
                    decoration: BoxDecoration(
                      color: const Color(0xFFE3E2E0),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  child: Row(
                    children: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('取消'),
                      ),
                      const Spacer(),
                      Text(
                        _selectionManager.selectedCount == 0
                            ? '选择照片'
                            : '已选 ${_selectionManager.selectedCount} 张',
                        style: const TextStyle(fontWeight: FontWeight.w600),
                      ),
                      const Spacer(),
                      TextButton(
                        onPressed:
                            (_selectionManager.selectedCount == 0 ||
                                _confirming)
                            ? null
                            : _confirm,
                        child: _confirming
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Color(0xFF2E7EED),
                                  ),
                                ),
                              )
                            : const Text('添加'),
                      ),
                    ],
                  ),
                ),
                const Divider(height: 1),
                Expanded(
                  child: _loading
                      ? const Center(child: CircularProgressIndicator())
                      : GridView.builder(
                          padding: const EdgeInsets.all(4),
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 4,
                                crossAxisSpacing: 2,
                                mainAxisSpacing: 2,
                              ),
                          itemCount: _photos.length,
                          itemBuilder: (context, index) {
                            final p = _photos[index];
                            final isSelected = _selectionManager.isSelected(
                              p.id,
                            );
                            final selectionOrder = _selectionManager
                                .getSelectionOrder(p.id);
                            return GestureDetector(
                              onTap: () {
                                setState(() {
                                  _selectionManager.toggleSelection(p.id);
                                });
                                HapticFeedback.selectionClick();
                              },
                              child: Stack(
                                fit: StackFit.expand,
                                children: [
                                  FutureBuilder<Widget>(
                                    future: _thumb(p),
                                    builder: (c, s) =>
                                        s.data ?? const SizedBox.shrink(),
                                  ),
                                  if (isSelected)
                                    Container(
                                      color: const Color(
                                        0xFF2E7EED,
                                      ).withValues(alpha: 0.25),
                                    ),
                                  Positioned(
                                    right: 6,
                                    top: 6,
                                    child: PhotoSelectionIndicator(
                                      selectionOrder: selectionOrder,
                                      isSelected: isSelected,
                                      size: 22.0, // 抖音风格稍小一些
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),

            // 确认处理中的覆盖层（iPad优化：防止黑屏误解）
            if (_confirming)
              Container(
                color: Colors.black.withValues(alpha: 0.5),
                child: const Center(
                  child: Card(
                    child: Padding(
                      padding: EdgeInsets.all(24),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Color(0xFF2E7EED),
                            ),
                          ),
                          SizedBox(height: 16),
                          Text(
                            '正在处理照片...',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF37352F),
                            ),
                          ),
                          SizedBox(height: 8),
                          Text(
                            '请稍候，正在准备您的照片',
                            style: TextStyle(
                              fontSize: 14,
                              color: Color(0xFF9B9A97),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class _CustomPhotoSelectorPageState extends State<CustomPhotoSelectorPage> {
  List<AssetEntity> _photos = [];
  bool _isLoading = true;
  bool _confirming = false; // 新增：确认中状态
  String? _errorMessage;

  // 状态管理：使用新的选择顺序管理器
  final PhotoSelectionOrderManager _selectionManager =
      PhotoSelectionOrderManager();

  @override
  void initState() {
    super.initState();
    _loadPhotos();
  }

  /// 加载相册照片
  Future<void> _loadPhotos() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // 获取相册列表
      final albums = await PhotoManager.getAssetPathList(
        type: RequestType.image,
        onlyAll: true,
      );

      if (albums.isNotEmpty) {
        // 获取所有照片
        final photos = await albums.first.getAssetListPaged(
          page: 0,
          size: 1000, // 限制数量避免内存问题
        );

        setState(() {
          _photos = photos;
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = '未找到相册';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = '加载照片失败: $e';
        _isLoading = false;
      });
      print('❌ 加载照片失败: $e');
    }
  }

  /// 处理照片点击 - 实现选择/取消选择功能
  void _onPhotoTap(AssetEntity photo) {
    setState(() {
      _selectionManager.toggleSelection(photo.id);
    });

    // 触觉反馈
    HapticFeedback.selectionClick();
  }

  /// 构建照片Widget
  Future<Widget> _buildPhotoWidget(AssetEntity photo) async {
    try {
      final thumbnailData = await photo.thumbnailDataWithSize(
        const ThumbnailSize(200, 200),
        quality: 80,
      );

      if (thumbnailData != null) {
        return Image.memory(
          thumbnailData,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: const Color(0xFFF5F5F5),
              child: const Icon(
                Icons.broken_image,
                color: Color(0xFF9B9A97),
                size: 32,
              ),
            );
          },
        );
      }
    } catch (e) {
      print('❌ 加载照片缩略图失败: $e');
    }

    return Container(
      color: const Color(0xFFF5F5F5),
      child: const Icon(Icons.broken_image, color: Color(0xFF9B9A97), size: 32),
    );
  }



  /// 确认选择 - Add按钮功能
  Future<void> _confirmSelection() async {
    if (_confirming) return; // 防止重复点击

    setState(() {
      _confirming = true;
    });

    try {
      print('📷 [CustomPicker] 开始处理选中的 ${_selectionManager.selectedCount} 张照片');

      final List<String> paths = [];
      final selectedIds = _selectionManager.selectedIds;

      // 分批处理照片，避免阻塞UI（iPad优化）
      const batchSize = 3; // 每批处理3张照片
      for (int i = 0; i < selectedIds.length; i += batchSize) {
        final batch = selectedIds.skip(i).take(batchSize).toList();

        print(
          '📷 [CustomPicker] 处理批次 ${(i / batchSize + 1).ceil()}/${(selectedIds.length / batchSize).ceil()}',
        );

        // 并行处理当前批次的照片
        final futures = batch.map((photoId) async {
          try {
            final photo = _photos.firstWhere((p) => p.id == photoId);
            final file = await photo.file;
            return file?.path;
          } catch (e) {
            print('⚠️ [CustomPicker] 获取照片文件失败: $e');
            return null;
          }
        }).toList();

        final batchPaths = await Future.wait(futures);

        // 添加成功获取的路径
        for (final path in batchPaths) {
          if (path != null) {
            paths.add(path);
          }
        }

        // 给UI一点时间更新（iPad优化）
        if (i + batchSize < selectedIds.length) {
          await Future.delayed(const Duration(milliseconds: 50));
        }
      }

      print('✅ [CustomPicker] 成功处理 ${paths.length}/${selectedIds.length} 张照片');

      if (mounted) {
        Navigator.of(context).pop(paths);
      }
    } catch (e) {
      print('❌ [CustomPicker] 照片处理失败: $e');

      if (mounted) {
        setState(() {
          _confirming = false;
        });

        // 显示错误提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('处理照片失败: $e'),
            backgroundColor: const Color(0xFFEB5757),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 使用系统全屏覆盖时，强制显示状态栏，避免被遮挡
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.manual,
      overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
    );

    // 修复布局问题：使用 Scaffold + SafeArea 确保所有内容显示在安全区域内
    return Scaffold(
      backgroundColor: Colors.white,
      // 修复交互问题：确保 AppBar 的 Cancel 按钮能正确执行 Navigator.pop
      appBar: AppBar(
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF37352F),
        elevation: 0,
        leading: TextButton(
          // 修复交互失效：确保 Cancel 按钮的 onPressed 能正确关闭页面
          onPressed: () => Navigator.of(context).pop(),
          child: const Text(
            'Cancel',
            style: TextStyle(color: Color(0xFF2E7EED), fontSize: 16),
          ),
        ),
        title: const Text(
          'Select Photos',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          // 修复健壮性：Add按钮在没有选择时禁用，有选择时激活
          TextButton(
            onPressed: (_selectionManager.selectedCount == 0 || _confirming)
                ? null
                : _confirmSelection,
            child: _confirming
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Color(0xFF2E7EED),
                      ),
                    ),
                  )
                : Text(
                    'Add',
                    style: TextStyle(
                      color: _selectionManager.selectedCount == 0
                          ? const Color(0xFF9B9A97)
                          : const Color(0xFF2E7EED),
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ],
      ),
      // 使用 SafeArea 确保内容不被刘海屏或状态栏遮挡
      body: SafeArea(top: true, bottom: true, child: _buildBody()),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF2E7EED)),
            ),
            SizedBox(height: 16),
            Text(
              '正在加载照片...',
              style: TextStyle(color: Color(0xFF6E6E6E), fontSize: 16),
            ),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Color(0xFFEB5757), size: 64),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(color: Color(0xFFEB5757), fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadPhotos,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2E7EED),
              ),
              child: const Text('重新加载', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      );
    }

    if (_photos.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.photo_library_outlined,
              color: Color(0xFF9B9A97),
              size: 64,
            ),
            SizedBox(height: 16),
            Text(
              '没有找到照片',
              style: TextStyle(color: Color(0xFF6E6E6E), fontSize: 16),
            ),
          ],
        ),
      );
    }

    return Stack(
      children: [
        Column(
          children: [
            // 选择状态提示
            if (_selectionManager.selectedCount > 0)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
                child: Text(
                  '已选择 ${_selectionManager.selectedCount} 张照片',
                  style: const TextStyle(
                    color: Color(0xFF2E7EED),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

            // 实现核心功能：GridView 显示照片网格
            Expanded(
              child: GridView.builder(
                padding: const EdgeInsets.all(8),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  crossAxisSpacing: 2,
                  mainAxisSpacing: 2,
                ),
                itemCount: _photos.length,
                itemBuilder: (context, index) {
                  final photo = _photos[index];
                  final isSelected = _selectionManager.isSelected(photo.id);
                  final selectionOrder = _selectionManager.getSelectionOrder(
                    photo.id,
                  );

                  // 使用 GestureDetector 包裹每个图片项实现点击选择
                  return GestureDetector(
                    onTap: () => _onPhotoTap(photo),
                    child: Stack(
                      fit: StackFit.expand,
                      children: [
                        // 照片显示
                        FutureBuilder<Widget>(
                          future: _buildPhotoWidget(photo),
                          builder: (context, snapshot) {
                            if (snapshot.hasData) {
                              return snapshot.data!;
                            }
                            return Container(
                              color: const Color(0xFFF5F5F5),
                              child: const Center(
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Color(0xFF2E7EED),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),

                        // 选择状态蒙层
                        if (isSelected)
                          Container(
                            decoration: BoxDecoration(
                              color: const Color(
                                0xFF2E7EED,
                              ).withValues(alpha: 0.3),
                            ),
                          ),

                        // 右上角数字指示器
                        Positioned(
                          top: 4,
                          right: 4,
                          child: PhotoSelectionIndicator(
                            selectionOrder: selectionOrder,
                            isSelected: isSelected,
                            size: 24.0,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),

        // 确认处理中的覆盖层（iPad优化：防止黑屏误解）
        if (_confirming)
          Container(
            color: Colors.black.withValues(alpha: 0.5),
            child: const Center(
              child: Card(
                child: Padding(
                  padding: EdgeInsets.all(24),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Color(0xFF2E7EED),
                        ),
                      ),
                      SizedBox(height: 16),
                      Text(
                        '正在处理照片...',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF37352F),
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        '请稍候，正在准备您的照片',
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFF9B9A97),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }
}
