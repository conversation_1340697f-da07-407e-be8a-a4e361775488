import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:oneday/features/photo_album/photo_album_creator_page.dart'
    show CustomPhotoSelectorPage;

/// 最简单的照片选择测试
class SimplePhotoTest extends StatefulWidget {
  const SimplePhotoTest({super.key});

  @override
  State<SimplePhotoTest> createState() => _SimplePhotoTestState();
}

class _SimplePhotoTestState extends State<SimplePhotoTest> {
  final ImagePicker _picker = ImagePicker();
  String? _imagePath;
  String _status = '点击按钮选择照片';

  Future<void> _pickImage() async {
    setState(() {
      _status = '正在选择照片...';
    });

    try {
      print('🔍 开始选择照片...');
      print('🔧 优先使用应用内可点击的自定义选择器');

      // 请求相册权限
      final permission = await PhotoManager.requestPermissionExtend();
      if (!permission.isAuth) {
        setState(() {
          _status = '需要相册权限，请在设置中授权';
        });
        return;
      }

      if (!mounted) return;
      // 打开应用内的自定义照片选择器（点击照片即可选中）
      final List<String>? paths = await Navigator.of(context)
          .push<List<String>>(
            MaterialPageRoute(builder: (_) => const CustomPhotoSelectorPage()),
          );

      if (paths != null && paths.isNotEmpty) {
        final firstPath = paths.first;
        print('✅ 自定义选择器选择成功: $firstPath');
        setState(() {
          _imagePath = firstPath;
          _status = '已选择 ${paths.length} 张，预览第一张';
        });
        return;
      }

      setState(() {
        _status = '未选择任何照片';
      });
    } catch (e) {
      // 兜底：回退到系统选择器
      print('⚠️ 自定义选择器失败，回退系统选择器: $e');
      try {
        final XFile? image = await _picker.pickImage(
          source: ImageSource.gallery,
          imageQuality: 80,
        );

        if (image != null) {
          print('✅ 照片选择成功: ${image.path}');
          setState(() {
            _imagePath = image.path;
            _status = '照片选择成功: ${image.name}';
          });
        } else {
          print('❌ 用户取消选择');
          setState(() {
            _status = '用户取消选择或相册为空';
          });
        }
      } catch (ee) {
        print('❌ 照片选择失败: $ee');
        setState(() {
          _status = '选择失败: ${ee.toString()}';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('简单照片测试'),
        backgroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // 状态显示
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _status,
                style: const TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
            ),

            const SizedBox(height: 20),

            // 选择按钮
            ElevatedButton(
              onPressed: _pickImage,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
              ),
              child: const Text('选择照片', style: TextStyle(fontSize: 18)),
            ),

            const SizedBox(height: 20),

            // 图片显示
            if (_imagePath != null)
              Expanded(
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      File(_imagePath!),
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        return const Center(child: Text('图片加载失败'));
                      },
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
