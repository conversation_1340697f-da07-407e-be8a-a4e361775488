import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:photo_manager/photo_manager.dart';
import 'dart:io';

/// Android权限诊断和修复工具
class AndroidPermissionChecker extends StatefulWidget {
  const AndroidPermissionChecker({super.key});

  @override
  State<AndroidPermissionChecker> createState() => _AndroidPermissionCheckerState();
}

class _AndroidPermissionCheckerState extends State<AndroidPermissionChecker> {
  final List<String> _logs = [];
  bool _isChecking = false;

  void _log(String message) {
    setState(() {
      _logs.add('${DateTime.now().toString().substring(11, 19)}: $message');
    });
    print('🔍 [AndroidPermission] $message');
  }

  void _clearLogs() {
    setState(() {
      _logs.clear();
    });
  }

  /// 全面检查Android权限状态
  Future<void> _comprehensivePermissionCheck() async {
    setState(() {
      _isChecking = true;
    });
    
    _clearLogs();
    _log('开始Android权限全面检查...');
    
    try {
      // 1. 平台检查
      _log('Platform.isAndroid: ${Platform.isAndroid}');
      _log('Platform.operatingSystem: ${Platform.operatingSystem}');
      _log('Platform.operatingSystemVersion: ${Platform.operatingSystemVersion}');
      
      if (!Platform.isAndroid) {
        _log('❌ 当前不是Android平台，跳过检查');
        return;
      }
      
      // 2. PhotoManager权限检查
      _log('');
      _log('=== PhotoManager权限检查 ===');
      try {
        final pmPermission = await PhotoManager.requestPermissionExtend();
        _log('PhotoManager权限状态: ${pmPermission.name}');
        _log('PhotoManager isAuth: ${pmPermission.isAuth}');
      } catch (e) {
        _log('❌ PhotoManager权限检查失败: $e');
      }
      
      // 3. permission_handler权限检查
      _log('');
      _log('=== permission_handler权限检查 ===');
      
      // Android 13+ 照片权限
      try {
        final photosStatus = await Permission.photos.status;
        _log('Permission.photos状态: ${photosStatus.name}');
        _log('Permission.photos.isGranted: ${photosStatus.isGranted}');
        _log('Permission.photos.isDenied: ${photosStatus.isDenied}');
        _log('Permission.photos.isPermanentlyDenied: ${photosStatus.isPermanentlyDenied}');
      } catch (e) {
        _log('⚠️ Permission.photos检查失败: $e');
      }
      
      // Android 12及以下 存储权限
      try {
        final storageStatus = await Permission.storage.status;
        _log('Permission.storage状态: ${storageStatus.name}');
        _log('Permission.storage.isGranted: ${storageStatus.isGranted}');
        _log('Permission.storage.isDenied: ${storageStatus.isDenied}');
        _log('Permission.storage.isPermanentlyDenied: ${storageStatus.isPermanentlyDenied}');
      } catch (e) {
        _log('⚠️ Permission.storage检查失败: $e');
      }
      
      // 4. 相机权限检查
      try {
        final cameraStatus = await Permission.camera.status;
        _log('Permission.camera状态: ${cameraStatus.name}');
        _log('Permission.camera.isGranted: ${cameraStatus.isGranted}');
      } catch (e) {
        _log('⚠️ Permission.camera检查失败: $e');
      }
      
      // 5. 尝试获取照片列表
      _log('');
      _log('=== 尝试获取照片列表 ===');
      try {
        final List<AssetPathEntity> albums = await PhotoManager.getAssetPathList(
          type: RequestType.image,
          hasAll: true,
        );
        _log('成功获取相册数量: ${albums.length}');
        
        if (albums.isNotEmpty) {
          final firstAlbum = albums.first;
          _log('第一个相册名称: ${firstAlbum.name}');
          
          final assetCount = await firstAlbum.assetCountAsync;
          _log('第一个相册照片数量: $assetCount');
          
          if (assetCount > 0) {
            final assets = await firstAlbum.getAssetListPaged(page: 0, size: 1);
            if (assets.isNotEmpty) {
              _log('✅ 成功获取照片样本');
            }
          }
        }
      } catch (e) {
        _log('❌ 获取照片列表失败: $e');
      }
      
      _log('');
      _log('=== 权限检查完成 ===');
      
    } catch (e) {
      _log('❌ 权限检查过程中出现异常: $e');
    } finally {
      setState(() {
        _isChecking = false;
      });
    }
  }

  /// 修复Android权限
  Future<void> _fixAndroidPermissions() async {
    setState(() {
      _isChecking = true;
    });
    
    _log('');
    _log('=== 开始修复Android权限 ===');
    
    try {
      if (!Platform.isAndroid) {
        _log('❌ 当前不是Android平台');
        return;
      }
      
      // 1. 先尝试PhotoManager权限请求
      _log('尝试PhotoManager权限请求...');
      final pmResult = await PhotoManager.requestPermissionExtend();
      _log('PhotoManager权限请求结果: ${pmResult.name}, isAuth: ${pmResult.isAuth}');
      
      if (pmResult.isAuth) {
        _log('✅ PhotoManager权限已获取');
        await _comprehensivePermissionCheck();
        return;
      }
      
      // 2. 尝试permission_handler权限请求
      _log('');
      _log('尝试permission_handler权限请求...');
      
      // 先尝试photos权限（Android 13+）
      try {
        _log('请求Permission.photos...');
        final photosResult = await Permission.photos.request();
        _log('Permission.photos请求结果: ${photosResult.name}');
        
        if (photosResult.isGranted) {
          _log('✅ Permission.photos权限已获取');
          await _comprehensivePermissionCheck();
          return;
        }
      } catch (e) {
        _log('⚠️ Permission.photos请求失败: $e');
      }
      
      // 再尝试storage权限（Android 12及以下）
      try {
        _log('请求Permission.storage...');
        final storageResult = await Permission.storage.request();
        _log('Permission.storage请求结果: ${storageResult.name}');
        
        if (storageResult.isGranted) {
          _log('✅ Permission.storage权限已获取');
          await _comprehensivePermissionCheck();
          return;
        }
        
        if (storageResult.isPermanentlyDenied) {
          _log('⚠️ 存储权限被永久拒绝，需要手动设置');
          await _showSettingsDialog();
        }
      } catch (e) {
        _log('⚠️ Permission.storage请求失败: $e');
      }
      
      _log('❌ 权限修复未成功，可能需要手动在设置中授权');
      
    } catch (e) {
      _log('❌ 权限修复过程中出现异常: $e');
    } finally {
      setState(() {
        _isChecking = false;
      });
    }
  }

  /// 显示设置引导对话框
  Future<void> _showSettingsDialog() async {
    if (!mounted) return;
    
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('权限被永久拒绝'),
        content: const Text(
          '相册权限被永久拒绝。\n\n'
          '请前往 设置 → 应用管理 → OneDay → 权限管理，\n'
          '开启"存储"或"照片"权限。',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await openAppSettings();
            },
            child: const Text('去设置'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Android权限诊断'),
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF37352F),
        elevation: 0,
      ),
      backgroundColor: const Color(0xFFF7F6F3),
      body: Column(
        children: [
          // 操作按钮区域
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isChecking ? null : _comprehensivePermissionCheck,
                    icon: Icon(_isChecking ? Icons.hourglass_empty : Icons.search),
                    label: Text(_isChecking ? '检查中...' : '全面检查权限'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF2E7EED),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isChecking ? null : _fixAndroidPermissions,
                    icon: const Icon(Icons.build),
                    label: const Text('修复权限'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF7C3AED),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // 清空日志按钮
          if (_logs.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Align(
                alignment: Alignment.centerRight,
                child: TextButton.icon(
                  onPressed: _clearLogs,
                  icon: const Icon(Icons.clear, size: 16),
                  label: const Text('清空日志'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.grey[600],
                  ),
                ),
              ),
            ),
          
          // 日志显示区域
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: _logs.isEmpty
                  ? const Center(
                      child: Text(
                        '点击"全面检查权限"开始诊断',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 16,
                        ),
                      ),
                    )
                  : ListView.builder(
                      itemCount: _logs.length,
                      itemBuilder: (context, index) {
                        final log = _logs[index];
                        Color textColor = Colors.white;
                        
                        if (log.contains('❌')) {
                          textColor = Colors.red[300]!;
                        } else if (log.contains('✅')) {
                          textColor = Colors.green[300]!;
                        } else if (log.contains('⚠️')) {
                          textColor = Colors.orange[300]!;
                        }
                        
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 1),
                          child: Text(
                            log,
                            style: TextStyle(
                              color: textColor,
                              fontSize: 12,
                              fontFamily: 'monospace',
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
