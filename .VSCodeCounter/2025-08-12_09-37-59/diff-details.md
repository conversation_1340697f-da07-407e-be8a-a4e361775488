# Diff Details

Date : 2025-08-12 09:37:59

Directory /Users/<USER>/development/flutter_apps/oneday

Total : 50 files,  7031 codes, 426 comments, 1223 blanks, all 8680 lines

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [EMERGENCY\_FIX\_TEST\_GUIDE.md](/EMERGENCY_FIX_TEST_GUIDE.md) | Markdown | 65 | 0 | 20 | 85 |
| [FORCE\_RESTART\_SCRIPT.md](/FORCE_RESTART_SCRIPT.md) | Markdown | 25 | 0 | 7 | 32 |
| [IMAGE\_GHOSTING\_FIX\_SUMMARY.md](/IMAGE_GHOSTING_FIX_SUMMARY.md) | Markdown | 115 | 0 | 32 | 147 |
| [PHOTO\_ALBUM\_CREATOR\_FIXES.md](/PHOTO_ALBUM_CREATOR_FIXES.md) | Markdown | 82 | 0 | 21 | 103 |
| [PHOTO\_POSITION\_FIX\_SUMMARY.md](/PHOTO_POSITION_FIX_SUMMARY.md) | Markdown | 109 | 0 | 34 | 143 |
| [PHOTO\_SELECTION\_DEBUG\_GUIDE.md](/PHOTO_SELECTION_DEBUG_GUIDE.md) | Markdown | 95 | 0 | 31 | 126 |
| [PHOTO\_SELECTION\_FIX\_COMPLETE.md](/PHOTO_SELECTION_FIX_COMPLETE.md) | Markdown | 161 | 0 | 41 | 202 |
| [PHOTO\_SELECTION\_FIX\_TEST\_GUIDE.md](/PHOTO_SELECTION_FIX_TEST_GUIDE.md) | Markdown | 124 | 0 | 35 | 159 |
| [PHOTO\_SELECTION\_FIX\_VERIFICATION.md](/PHOTO_SELECTION_FIX_VERIFICATION.md) | Markdown | 94 | 0 | 24 | 118 |
| [PHOTO\_SELECTION\_LAYER\_CONFLICT\_FIX\_GUIDE.md](/PHOTO_SELECTION_LAYER_CONFLICT_FIX_GUIDE.md) | Markdown | 95 | 0 | 26 | 121 |
| [PHOTO\_SELECTION\_ORDER\_REFACTOR\_COMPLETE.md](/PHOTO_SELECTION_ORDER_REFACTOR_COMPLETE.md) | Markdown | 115 | 0 | 34 | 149 |
| [docs/PHOTO\_SELECTION\_TEST\_GUIDE.md](/docs/PHOTO_SELECTION_TEST_GUIDE.md) | Markdown | 94 | 0 | 26 | 120 |
| [docs/fixes/IOS\_PHOTO\_SELECTION\_COMPREHENSIVE\_SOLUTION.md](/docs/fixes/IOS_PHOTO_SELECTION_COMPREHENSIVE_SOLUTION.md) | Markdown | 204 | 0 | 50 | 254 |
| [docs/fixes/IOS\_SIMULATOR\_PHOTO\_SELECTION\_FIX.md](/docs/fixes/IOS_SIMULATOR_PHOTO_SELECTION_FIX.md) | Markdown | 183 | 0 | 65 | 248 |
| [docs/fixes/photo\_album\_dialog\_position\_fix.md](/docs/fixes/photo_album_dialog_position_fix.md) | Markdown | 100 | 0 | 31 | 131 |
| [docs/testing/IOS\_SIMULATOR\_PHOTO\_FIX\_TEST\_GUIDE.md](/docs/testing/IOS_SIMULATOR_PHOTO_FIX_TEST_GUIDE.md) | Markdown | 158 | 0 | 50 | 208 |
| [docs/testing/IOS\_SIMULATOR\_PHOTO\_TESTING\_GUIDE.md](/docs/testing/IOS_SIMULATOR_PHOTO_TESTING_GUIDE.md) | Markdown | 144 | 0 | 39 | 183 |
| [docs/troubleshooting/PHOTO\_SELECTION\_CANNOT\_CONFIRM\_ISSUE.md](/docs/troubleshooting/PHOTO_SELECTION_CANNOT_CONFIRM_ISSUE.md) | Markdown | 179 | 0 | 47 | 226 |
| [lib/debug/dialog\_safe\_area\_test\_page.dart](/lib/debug/dialog_safe_area_test_page.dart) | Dart | 356 | 12 | 21 | 389 |
| [lib/debug/image\_picker\_debug\_page.dart](/lib/debug/image_picker_debug_page.dart) | Dart | 33 | 2 | 4 | 39 |
| [lib/debug/ios\_photo\_selection\_debugger.dart](/lib/debug/ios_photo_selection_debugger.dart) | Dart | 341 | 28 | 50 | 419 |
| [lib/debug/ios\_simulator\_test\_page.dart](/lib/debug/ios_simulator_test_page.dart) | Dart | 467 | 25 | 47 | 539 |
| [lib/debug/photo\_rendering\_debugger.dart](/lib/debug/photo_rendering_debugger.dart) | Dart | 366 | 26 | 48 | 440 |
| [lib/debug/photo\_selection\_advanced\_diagnostics.dart](/lib/debug/photo_selection_advanced_diagnostics.dart) | Dart | 439 | 48 | 74 | 561 |
| [lib/debug/photo\_selection\_validator.dart](/lib/debug/photo_selection_validator.dart) | Dart | 168 | 18 | 35 | 221 |
| [lib/debug/route\_debug\_page.dart](/lib/debug/route_debug_page.dart) | Dart | 15 | 0 | 0 | 15 |
| [lib/debug/simple\_image\_picker\_test.dart](/lib/debug/simple_image_picker_test.dart) | Dart | 209 | 11 | 27 | 247 |
| [lib/debug/simple\_photo\_test.dart](/lib/debug/simple_photo_test.dart) | Dart | 137 | 7 | 16 | 160 |
| [lib/debug/simulator\_reset\_helper.dart](/lib/debug/simulator_reset_helper.dart) | Dart | 407 | 25 | 40 | 472 |
| [lib/features/auth/phone\_login\_page.dart](/lib/features/auth/phone_login_page.dart) | Dart | -43 | 3 | -5 | -45 |
| [lib/features/auth/providers/phone\_login\_provider.dart](/lib/features/auth/providers/phone_login_provider.dart) | Dart | -34 | -5 | -6 | -45 |
| [lib/features/auth/register\_page.dart](/lib/features/auth/register_page.dart) | Dart | -7 | -1 | -1 | -9 |
| [lib/features/home/<USER>/lib/features/home/<USER>
| [lib/features/main/main\_container\_page.dart](/lib/features/main/main_container_page.dart) | Dart | 0 | 1 | 1 | 2 |
| [lib/features/memory\_palace/palace\_manager\_page.dart](/lib/features/memory_palace/palace_manager_page.dart) | Dart | 53 | 9 | 8 | 70 |
| [lib/features/memory\_palace/scene\_detail\_page.dart](/lib/features/memory_palace/scene_detail_page.dart) | Dart | 42 | 9 | 5 | 56 |
| [lib/features/photo\_album/photo\_album\_creator\_page.dart](/lib/features/photo_album/photo_album_creator_page.dart) | Dart | 849 | 67 | 74 | 990 |
| [lib/features/profile/pages/profile\_edit\_page.dart](/lib/features/profile/pages/profile_edit_page.dart) | Dart | 50 | 7 | 5 | 62 |
| [lib/features/settings/settings\_page.dart](/lib/features/settings/settings_page.dart) | Dart | 17 | 1 | 1 | 19 |
| [lib/main.dart](/lib/main.dart) | Dart | 4 | 0 | 0 | 4 |
| [lib/router/app\_router.dart](/lib/router/app_router.dart) | Dart | 35 | 5 | 7 | 47 |
| [lib/utils/image\_rendering\_fix.dart](/lib/utils/image_rendering_fix.dart) | Dart | 135 | 10 | 10 | 155 |
| [lib/utils/ios\_simulator\_detector.dart](/lib/utils/ios_simulator_detector.dart) | Dart | 157 | 20 | 30 | 207 |
| [lib/utils/safe\_area\_helper.dart](/lib/utils/safe_area_helper.dart) | Dart | 475 | 66 | 65 | 606 |
| [macos/Flutter/GeneratedPluginRegistrant.swift](/macos/Flutter/GeneratedPluginRegistrant.swift) | Swift | 4 | 0 | 0 | 4 |
| [pubspec.yaml](/pubspec.yaml) | YAML | 2 | 2 | 2 | 6 |
| [tempPrompts.md](/tempPrompts.md) | Markdown | 19 | 0 | 6 | 25 |
| [test/photo\_album\_position\_fix\_test.dart](/test/photo_album_position_fix_test.dart) | Dart | 32 | 7 | 11 | 50 |
| [test/photo\_selection\_order\_test.dart](/test/photo_selection_order_test.dart) | Dart | 78 | 14 | 21 | 113 |
| [test/photo\_selection\_unit\_test.dart](/test/photo_selection_unit_test.dart) | Dart | 66 | 8 | 12 | 86 |

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details