"filename", "language", "Markdown", "<PERSON><PERSON><PERSON>", "C++", "<PERSON><PERSON><PERSON>", "Dar<PERSON>", "Python", "Ruby", "HTML", "JSON", "Swift", "XML", "Java Properties", "comment", "blank", "total"
"/Users/<USER>/development/flutter_apps/oneday/.augment/rules/rules.md", "Markdown", 387, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 114, 501
"/Users/<USER>/development/flutter_apps/oneday/.cursorrules.md", "Markdown", 436, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 123, 559
"/Users/<USER>/development/flutter_apps/oneday/ARCHITECTURE.md", "Markdown", 214, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 55, 269
"/Users/<USER>/development/flutter_apps/oneday/EMERGENCY_FIX_TEST_GUIDE.md", "Markdown", 65, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 85
"/Users/<USER>/development/flutter_apps/oneday/FEATURES.md", "Markdown", 395, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 105, 500
"/Users/<USER>/development/flutter_apps/oneday/FORCE_RESTART_SCRIPT.md", "Markdown", 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 32
"/Users/<USER>/development/flutter_apps/oneday/IMAGE_GHOSTING_FIX_SUMMARY.md", "Markdown", 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 147
"/Users/<USER>/development/flutter_apps/oneday/PHOTO_ALBUM_CREATOR_FIXES.md", "Markdown", 82, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 103
"/Users/<USER>/development/flutter_apps/oneday/PHOTO_POSITION_FIX_SUMMARY.md", "Markdown", 109, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 143
"/Users/<USER>/development/flutter_apps/oneday/PHOTO_SELECTION_DEBUG_GUIDE.md", "Markdown", 95, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 126
"/Users/<USER>/development/flutter_apps/oneday/PHOTO_SELECTION_FIX_COMPLETE.md", "Markdown", 161, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 202
"/Users/<USER>/development/flutter_apps/oneday/PHOTO_SELECTION_FIX_TEST_GUIDE.md", "Markdown", 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 159
"/Users/<USER>/development/flutter_apps/oneday/PHOTO_SELECTION_FIX_VERIFICATION.md", "Markdown", 94, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 118
"/Users/<USER>/development/flutter_apps/oneday/PHOTO_SELECTION_LAYER_CONFLICT_FIX_GUIDE.md", "Markdown", 95, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 121
"/Users/<USER>/development/flutter_apps/oneday/PHOTO_SELECTION_ORDER_REFACTOR_COMPLETE.md", "Markdown", 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 149
"/Users/<USER>/development/flutter_apps/oneday/PROBLEM_SOLUTIONS.md", "Markdown", 476, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 131, 607
"/Users/<USER>/development/flutter_apps/oneday/README.md", "Markdown", 1182, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 261, 1443
"/Users/<USER>/development/flutter_apps/oneday/analysis_options.yaml", "YAML", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 21, 4, 29
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/debug/AndroidManifest.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 4, 1, 8
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/main/AndroidManifest.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 0, 14, 5, 65
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/drawable-v21/launch_background.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 7, 2, 13
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/drawable/launch_background.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 7, 2, 13
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/values-night/styles.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 9, 1, 19
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/values/styles.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 9, 1, 19
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/profile/AndroidManifest.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 4, 1, 8
"/Users/<USER>/development/flutter_apps/oneday/android/gradle.properties", "Java Properties", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 1, 4
"/Users/<USER>/development/flutter_apps/oneday/android/gradle/wrapper/gradle-wrapper.properties", "Java Properties", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 1, 6
"/Users/<USER>/development/flutter_apps/oneday/assets/data/Action.html", "HTML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"/Users/<USER>/development/flutter_apps/oneday/assets/data/prefixes.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 88, 0, 0, 0, 0, 1, 89
"/Users/<USER>/development/flutter_apps/oneday/assets/data/profanity_words.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 259, 0, 0, 0, 0, 1, 260
"/Users/<USER>/development/flutter_apps/oneday/assets/data/suffixes.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 99, 0, 0, 0, 0, 1, 100
"/Users/<USER>/development/flutter_apps/oneday/assets/data/vocabulary.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 84516, 0, 0, 0, 0, 0, 84516
"/Users/<USER>/development/flutter_apps/oneday/assets/data/word_roots.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 158, 0, 0, 0, 0, 1, 159
"/Users/<USER>/development/flutter_apps/oneday/assets/data/动作.html", "HTML", 0, 0, 0, 0, 0, 0, 0, 693, 0, 0, 0, 0, 23, 53, 769
"/Users/<USER>/development/flutter_apps/oneday/assets/icons/README.md", "Markdown", 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 46
"/Users/<USER>/development/flutter_apps/oneday/build.yaml", "YAML", 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 6, 1, 18
"/Users/<USER>/development/flutter_apps/oneday/devtools_options.yaml", "YAML", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4
"/Users/<USER>/development/flutter_apps/oneday/docs/PHOTO_SELECTION_TEST_GUIDE.md", "Markdown", 94, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 120
"/Users/<USER>/development/flutter_apps/oneday/docs/README.md", "Markdown", 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 91
"/Users/<USER>/development/flutter_apps/oneday/docs/business/COMMUNITY_BUSINESS_PLAN.md", "Markdown", 403, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 130, 533
"/Users/<USER>/development/flutter_apps/oneday/docs/business/FINANCIAL_MODEL_ANALYSIS.md", "Markdown", 184, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 60, 244
"/Users/<USER>/development/flutter_apps/oneday/docs/business/IMPLEMENTATION_CHECKLIST.md", "Markdown", 245, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 304
"/Users/<USER>/development/flutter_apps/oneday/docs/business/RISK_ASSESSMENT_STRATEGY.md", "Markdown", 242, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 77, 319
"/Users/<USER>/development/flutter_apps/oneday/docs/community/USER_COMMUNITY_TEMPLATE.md", "Markdown", 261, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 122, 383
"/Users/<USER>/development/flutter_apps/oneday/docs/core/ARCHITECTURE.md", "Markdown", 380, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 461
"/Users/<USER>/development/flutter_apps/oneday/docs/core/FEATURES.md", "Markdown", 601, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 721
"/Users/<USER>/development/flutter_apps/oneday/docs/core/NOTION_PROJECT_TEMPLATE.md", "Markdown", 137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 58, 195
"/Users/<USER>/development/flutter_apps/oneday/docs/core/ONEDAY_PROJECT_ANALYSIS.md", "Markdown", 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 47, 152
"/Users/<USER>/development/flutter_apps/oneday/docs/core/PRD.md", "Markdown", 339, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 97, 436
"/Users/<USER>/development/flutter_apps/oneday/docs/core/README.md", "Markdown", 32, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 42
"/Users/<USER>/development/flutter_apps/oneday/docs/development/ABILITY_RADAR_FEATURE.md", "Markdown", 137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 47, 184
"/Users/<USER>/development/flutter_apps/oneday/docs/development/COMMUNITY_ARTICLE_IMPORT_FEATURE.md", "Markdown", 118, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 155
"/Users/<USER>/development/flutter_apps/oneday/docs/development/CUSTOM_EXERCISE_CATEGORY_FEATURE.md", "Markdown", 193, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 236
"/Users/<USER>/development/flutter_apps/oneday/docs/development/CUSTOM_LIBRARY_EDITOR_IMPROVEMENTS.md", "Markdown", 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 128
"/Users/<USER>/development/flutter_apps/oneday/docs/development/EDIT_ALBUM_ENHANCEMENT.md", "Markdown", 167, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 210
"/Users/<USER>/development/flutter_apps/oneday/docs/development/FLOATING_TIMER_FEATURE.md", "Markdown", 74, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 94
"/Users/<USER>/development/flutter_apps/oneday/docs/development/FLOATING_TIMER_SYSTEM_LEVEL_IMPLEMENTATION.md", "Markdown", 160, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 40, 202
"/Users/<USER>/development/flutter_apps/oneday/docs/development/INTERNATIONALIZATION_IMPLEMENTATION.md", "Markdown", 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 163
"/Users/<USER>/development/flutter_apps/oneday/docs/development/NOTION_STYLE_UPDATES.md", "Markdown", 125, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 44, 169
"/Users/<USER>/development/flutter_apps/oneday/docs/development/OPTIMIZING_CURSOR_AI.md", "Markdown", 182, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 58, 240
"/Users/<USER>/development/flutter_apps/oneday/docs/development/POMODORO_TIMER_IMPLEMENTATION.md", "Markdown", 155, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 182
"/Users/<USER>/development/flutter_apps/oneday/docs/development/PROFANITY_FILTER_DEBUG_GUIDE.md", "Markdown", 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 146
"/Users/<USER>/development/flutter_apps/oneday/docs/development/PROFANITY_FILTER_SYSTEM_DESIGN.md", "Markdown", 206, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 259
"/Users/<USER>/development/flutter_apps/oneday/docs/development/README.md", "Markdown", 41, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 59
"/Users/<USER>/development/flutter_apps/oneday/docs/development/REMOVE_CAMERA_FEATURE.md", "Markdown", 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 140
"/Users/<USER>/development/flutter_apps/oneday/docs/development/SHARE_FUNCTION_SIMPLIFICATION.md", "Markdown", 99, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 128
"/Users/<USER>/development/flutter_apps/oneday/docs/development/SHARE_UI_RESTORE_FEATURE.md", "Markdown", 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 155
"/Users/<USER>/development/flutter_apps/oneday/docs/development/STUDY_STATISTICS_REALTIME_UPDATE_IMPLEMENTATION.md", "Markdown", 145, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 188
"/Users/<USER>/development/flutter_apps/oneday/docs/development/SWIPE_GESTURE_DEMO.md", "Markdown", 216, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 264
"/Users/<USER>/development/flutter_apps/oneday/docs/development/VOCABULARY_SCROLLBAR_ENHANCEMENT.md", "Markdown", 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 164
"/Users/<USER>/development/flutter_apps/oneday/docs/development/WORD_DISPLAY_LOWERCASE_UPDATE.md", "Markdown", 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 147
"/Users/<USER>/development/flutter_apps/oneday/docs/development/blueprint.md", "Markdown", 69, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 88
"/Users/<USER>/development/flutter_apps/oneday/docs/features/CREATE_ACTION_LIBRARY_BUTTON_GUIDE.md", "Markdown", 142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 179
"/Users/<USER>/development/flutter_apps/oneday/docs/features/CREATE_ACTION_LIBRARY_BUTTON_IMPLEMENTATION_SUMMARY.md", "Markdown", 204, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45, 249
"/Users/<USER>/development/flutter_apps/oneday/docs/features/CUSTOM_ACTION_LIBRARY_IMPLEMENTATION.md", "Markdown", 183, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 225
"/Users/<USER>/development/flutter_apps/oneday/docs/features/CUSTOM_ACTION_LIBRARY_USER_GUIDE.md", "Markdown", 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 177
"/Users/<USER>/development/flutter_apps/oneday/docs/features/DEVELOPER_TOOLS_UPDATE.md", "Markdown", 149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 197
"/Users/<USER>/development/flutter_apps/oneday/docs/features/DIALOG_BACKGROUND_BEFORE_AFTER_COMPARISON.md", "Markdown", 215, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 274
"/Users/<USER>/development/flutter_apps/oneday/docs/features/DIALOG_BACKGROUND_UNIFICATION.md", "Markdown", 213, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 63, 276
"/Users/<USER>/development/flutter_apps/oneday/docs/features/EXPORT_FUNCTIONALITY.md", "Markdown", 160, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 191
"/Users/<USER>/development/flutter_apps/oneday/docs/features/FINAL_DIALOG_BACKGROUND_REPORT.md", "Markdown", 145, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 40, 185
"/Users/<USER>/development/flutter_apps/oneday/docs/features/GRADUATE_VOCABULARY_MANAGER.md", "Markdown", 137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 179
"/Users/<USER>/development/flutter_apps/oneday/docs/features/HELP_FEEDBACK.md", "Markdown", 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 137
"/Users/<USER>/development/flutter_apps/oneday/docs/features/NEW_STORE_ITEMS.md", "Markdown", 125, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 40, 165
"/Users/<USER>/development/flutter_apps/oneday/docs/features/PROFILE_DATA_SYNC.md", "Markdown", 129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 39, 168
"/Users/<USER>/development/flutter_apps/oneday/docs/features/SILENT_HOME_REFRESH.md", "Markdown", 110, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 147
"/Users/<USER>/development/flutter_apps/oneday/docs/features/SILENT_REFRESH_OPTIMIZATION.md", "Markdown", 175, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 44, 219
"/Users/<USER>/development/flutter_apps/oneday/docs/features/VOCABULARY_MANAGER_IMPLEMENTATION_SUMMARY.md", "Markdown", 156, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 198
"/Users/<USER>/development/flutter_apps/oneday/docs/features/WORD_DETAIL_DIALOG_IMPLEMENTATION.md", "Markdown", 214, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 57, 271
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/ACHIEVEMENT_UNLOCK_NAVIGATION_FIX.md", "Markdown", 161, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 40, 201
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/ACTION_LIBRARY_SELECTOR_OVERFLOW_FIX.md", "Markdown", 168, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 203
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/BUBBLE_BORDER_CONSISTENCY_FIX.md", "Markdown", 135, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 177
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/BUBBLE_CLICK_POSITION_FINAL_ALIGNMENT_FIX.md", "Markdown", 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 158
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/BUBBLE_POSITIONING_FINAL_FIX.md", "Markdown", 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 149
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/BUBBLE_POSITIONING_PRECISE_FIX.md", "Markdown", 130, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 173
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/BUBBLE_POSITION_ADJUSTMENT.md", "Markdown", 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 129
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/CLICK_POSITION_FIX.md", "Markdown", 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 163
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/COMPRESSED_IMAGE_OFFSET_FIX.md", "Markdown", 110, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 144
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/COORDINATE_SYSTEM_FINAL_FIX.md", "Markdown", 111, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 149
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/CUSTOM_ACTION_EDIT_SAVE_FIX.md", "Markdown", 131, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 166
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/DRAG_POSITION_JUMP_FIX.md", "Markdown", 106, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 135
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/DYNAMIC_OFFSET_ADJUSTMENT_FIX.md", "Markdown", 129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 166
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/EXERCISE_LIBRARY_PAGE_IMPORT_FIX.md", "Markdown", 91, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 134
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/FINAL_OFFSET_ADJUSTMENT_WITH_DEBUG.md", "Markdown", 123, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 158
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/FINAL_PRECISION_ADJUSTMENT.md", "Markdown", 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 131
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/FINE_TUNING_OFFSET_ADJUSTMENT.md", "Markdown", 90, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 119
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/IOS_PHOTO_SELECTION_COMPREHENSIVE_SOLUTION.md", "Markdown", 204, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 254
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/IOS_SIMULATOR_PHOTO_SELECTION_FIX.md", "Markdown", 183, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 65, 248
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/PROFILE_AVATAR_CLICK_UPDATE.md", "Markdown", 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 145
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/SNACKBAR_AND_COMMUNITY_FIXES.md", "Markdown", 163, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 52, 215
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/STANDARDIZED_COORDINATE_SYSTEM_IMPLEMENTATION.md", "Markdown", 146, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 192
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/UNIVERSAL_COMPRESSED_IMAGE_FIX_VERIFICATION.md", "Markdown", 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 133
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/USER_IMPORTED_PHOTOS_FIX.md", "Markdown", 146, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 44, 190
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/photo_album_dialog_position_fix.md", "Markdown", 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 131
"/Users/<USER>/development/flutter_apps/oneday/docs/guides/CROSS_ORIENTATION_POSITIONING_SOLUTION_TEMPLATE.md", "Markdown", 471, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 109, 580
"/Users/<USER>/development/flutter_apps/oneday/docs/guides/DEVELOPER_ENTRANCE_GUIDE.md", "Markdown", 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 83
"/Users/<USER>/development/flutter_apps/oneday/docs/guides/FLOATING_TIMER_DEBUG_GUIDE.md", "Markdown", 199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 60, 259
"/Users/<USER>/development/flutter_apps/oneday/docs/guides/ICON_SETUP_GUIDE.md", "Markdown", 231, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 259
"/Users/<USER>/development/flutter_apps/oneday/docs/guides/IMAGE_PICKER_DEBUG_GUIDE.md", "Markdown", 181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 44, 225
"/Users/<USER>/development/flutter_apps/oneday/docs/guides/POSITIONING_QUICK_REFERENCE.md", "Markdown", 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 166
"/Users/<USER>/development/flutter_apps/oneday/docs/guides/PROFILE_EDIT_VERIFICATION_GUIDE.md", "Markdown", 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 49, 170
"/Users/<USER>/development/flutter_apps/oneday/docs/guides/README.md", "Markdown", 91, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 118
"/Users/<USER>/development/flutter_apps/oneday/docs/guides/SYSTEM_FLOATING_TIMER_USER_GUIDE.md", "Markdown", 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 158
"/Users/<USER>/development/flutter_apps/oneday/docs/guides/TIMER_FLOATING_WINDOW_GUIDE.md", "Markdown", 69, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 96
"/Users/<USER>/development/flutter_apps/oneday/docs/guides/UI_DEMO_GUIDE.md", "Markdown", 91, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 113
"/Users/<USER>/development/flutter_apps/oneday/docs/releases/README.md", "Markdown", 107, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 137
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/ACHIEVEMENT_SYSTEM_TEST_GUIDE.md", "Markdown", 87, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 115
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/BUBBLE_OFFSET_DEBUGGING.md", "Markdown", 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 151
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/COMPREHENSIVE_TEST_GUIDE.md", "Markdown", 276, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 96, 372
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/COVER_MODE_TEST_GUIDE.md", "Markdown", 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 150
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/DEBUGGING_STEP_1_TEST_GUIDE.md", "Markdown", 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 142
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/DEBUG_PHASE_1_COMPLETE.md", "Markdown", 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 124
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/DRAGGABLE_ANNOTATION_SYSTEM_TEST.md", "Markdown", 162, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 47, 209
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/DRAG_FEATURE_DEBUG_GUIDE.md", "Markdown", 138, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 171
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/FLOATING_WINDOW_TEST_CHECKLIST.md", "Markdown", 114, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 141
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/IOS_SIMULATOR_PHOTO_FIX_TEST_GUIDE.md", "Markdown", 158, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 208
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/IOS_SIMULATOR_PHOTO_TESTING_GUIDE.md", "Markdown", 144, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 39, 183
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/MEMORY_ALBUM_DRAG_FEATURE_TEST.md", "Markdown", 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 133
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/POMODORO_TEST_GUIDE.md", "Markdown", 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 125
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/QUICK_TEST_STEPS.md", "Markdown", 85, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 111
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/README.md", "Markdown", 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 122
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/STUDY_SESSION_COMPLETION_VERIFICATION.md", "Markdown", 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 191
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/TEMP_BUBBLE_DRAG_DEBUG_GUIDE.md", "Markdown", 125, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 159
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/TESTING_GUIDE.md", "Markdown", 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 137
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/TIMER_TEST_GUIDE.md", "Markdown", 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 153
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/USER_IMPORTED_PHOTOS_DEBUG_GUIDE.md", "Markdown", 111, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 144
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/ANNOTATION_SCALE_FIX_SUMMARY.md", "Markdown", 154, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 197
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/ANNOTATION_SCALE_FIX_TEST_GUIDE.md", "Markdown", 144, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 187
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/BOTTOM_NAVIGATION_BAR_FIX.md", "Markdown", 244, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 294
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/CALENDAR_TASK_DISPLAY_FIX.md", "Markdown", 219, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 39, 258
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/COLOR_SYSTEM_RESTORATION.md", "Markdown", 134, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 168
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/COMPREHENSIVE_NAVIGATION_AUDIT.md", "Markdown", 192, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 240
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/DIALOG_LAYOUT_BUGFIX.md", "Markdown", 206, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 248
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/DIALOG_LAYOUT_OPTIMIZATION.md", "Markdown", 192, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 238
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/EXERCISE_LIBRARY_UI_OPTIMIZATION.md", "Markdown", 111, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 147
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/FINAL_NAVIGATION_TEST_REPORT.md", "Markdown", 118, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 156
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/FLOATING_TIMER_FIXES_VERIFICATION.md", "Markdown", 190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 240
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/FLOATING_WINDOW_FINAL_OPTIMIZATION.md", "Markdown", 142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 177
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/FLOATING_WINDOW_OPTIMIZATION_SUMMARY.md", "Markdown", 129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 164
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/HOW_TO_TEST_NAVIGATION_FIX.md", "Markdown", 147, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 198
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/IPAD_STAGE_MANAGER_LAYOUT_FIX.md", "Markdown", 200, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 231
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/KNOWLEDGE_POINT_SYNC_FIX.md", "Markdown", 191, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 233
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/NAVIGATION_FIX_PROGRESS_REPORT.md", "Markdown", 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 159
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/NAVIGATION_FIX_SUMMARY.md", "Markdown", 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 39, 172
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/PDF_COLOR_RESTORATION.md", "Markdown", 160, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 201
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/PHOTO_SELECTION_CANNOT_CONFIRM_ISSUE.md", "Markdown", 179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 47, 226
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/PROBLEM_SOLUTIONS.md", "Markdown", 317, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 112, 429
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/RADAR_CHART_TECHNICAL_ANALYSIS.md", "Markdown", 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 132
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/README.md", "Markdown", 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 76
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/SIDEBAR_BUTTON_OPTIMIZATION.md", "Markdown", 147, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 184
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/TIMEBOX_REST_SKIP_NAVIGATION_FIX.md", "Markdown", 157, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 52, 209
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/TIMEBOX_TIMER_FIXES.md", "Markdown", 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 142
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/TIMER_FIXES_PROGRESS.md", "Markdown", 131, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 166
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/UI_OVERFLOW_FIXES_SUMMARY.md", "Markdown", 98, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 127
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/VOCABULARY_SERVICE_FIX.md", "Markdown", 219, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 52, 271
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/WORD_TRAINING_OPTIMIZATION.md", "Markdown", 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 39, 147
"/Users/<USER>/development/flutter_apps/oneday/docs/ui_improvements/CATEGORY_SELECTOR_PURE_TEXT_FIX.md", "Markdown", 118, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 146
"/Users/<USER>/development/flutter_apps/oneday/docs/ui_improvements/IMAGE_COMPRESSION_IMPROVEMENTS.md", "Markdown", 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 141
"/Users/<USER>/development/flutter_apps/oneday/docs/ui_improvements/IPAD_CALENDAR_OVERFLOW_FIX_COMPLETE.md", "Markdown", 129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 162
"/Users/<USER>/development/flutter_apps/oneday/docs/ui_improvements/IPAD_CALENDAR_RESPONSIVE_FIX.md", "Markdown", 178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 216
"/Users/<USER>/development/flutter_apps/oneday/docs/ui_improvements/REMOVE_DUPLICATE_MENU_ITEMS.md", "Markdown", 111, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 147
"/Users/<USER>/development/flutter_apps/oneday/docs/ui_improvements/UI_IMPROVEMENTS.md", "Markdown", 116, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 158
"/Users/<USER>/development/flutter_apps/oneday/example/achievement_wage_example.dart", "Dart", 0, 0, 0, 0, 106, 0, 0, 0, 0, 0, 0, 0, 19, 28, 153
"/Users/<USER>/development/flutter_apps/oneday/example/calendar_responsive_demo.dart", "Dart", 0, 0, 0, 0, 342, 0, 0, 0, 0, 0, 0, 0, 16, 24, 382
"/Users/<USER>/development/flutter_apps/oneday/example/calendar_task_display_demo.dart", "Dart", 0, 0, 0, 0, 396, 0, 0, 0, 0, 0, 0, 0, 20, 25, 441
"/Users/<USER>/development/flutter_apps/oneday/example/color_scheme_comparison_demo.dart", "Dart", 0, 0, 0, 0, 434, 0, 0, 0, 0, 0, 0, 0, 16, 31, 481
"/Users/<USER>/development/flutter_apps/oneday/example/navigation_bottom_bar_demo.dart", "Dart", 0, 0, 0, 0, 366, 0, 0, 0, 0, 0, 0, 0, 9, 15, 390
"/Users/<USER>/development/flutter_apps/oneday/example/responsive_layout_demo.dart", "Dart", 0, 0, 0, 0, 560, 0, 0, 0, 0, 0, 0, 0, 13, 26, 599
"/Users/<USER>/development/flutter_apps/oneday/example/study_session_completion_demo.dart", "Dart", 0, 0, 0, 0, 429, 0, 0, 0, 0, 0, 0, 0, 7, 24, 460
"/Users/<USER>/development/flutter_apps/oneday/example/timebox_rest_skip_demo.dart", "Dart", 0, 0, 0, 0, 458, 0, 0, 0, 0, 0, 0, 0, 24, 40, 522
"/Users/<USER>/development/flutter_apps/oneday/ios/OneDayWidget/AppIntent.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 7, 4, 19
"/Users/<USER>/development/flutter_apps/oneday/ios/OneDayWidget/OneDayWidgetBundle.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 6, 3, 17
"/Users/<USER>/development/flutter_apps/oneday/ios/OneDayWidget/OneDayWidgetControl.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 56, 0, 0, 7, 15, 78
"/Users/<USER>/development/flutter_apps/oneday/ios/Podfile", "Ruby", 0, 0, 0, 0, 0, 0, 32, 0, 0, 0, 0, 0, 2, 10, 44
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/AppDelegate.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 2, 14
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 116, 0, 0, 0, 0, 1, 117
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 23, 0, 0, 0, 0, 1, 24
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md", "Markdown", 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 5
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/Base.lproj/LaunchScreen.storyboard", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 0, 1, 1, 38
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/Base.lproj/Main.storyboard", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 0, 1, 1, 27
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/Runner-Bridging-Header.h", "C++", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/development/flutter_apps/oneday/ios/RunnerTests/RunnerTests.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 0, 2, 4, 13
"/Users/<USER>/development/flutter_apps/oneday/l10n.yaml", "YAML", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5
"/Users/<USER>/development/flutter_apps/oneday/lib/core/constants/app_icons.dart", "Dart", 0, 0, 0, 0, 35, 0, 0, 0, 0, 0, 0, 0, 5, 3, 43
"/Users/<USER>/development/flutter_apps/oneday/lib/core/data/pao_exercises_data.dart", "Dart", 0, 0, 0, 0, 1587, 0, 0, 0, 0, 0, 0, 0, 28, 26, 1641
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/dialog_safe_area_test_page.dart", "Dart", 0, 0, 0, 0, 356, 0, 0, 0, 0, 0, 0, 0, 12, 21, 389
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/image_cropper_test_page.dart", "Dart", 0, 0, 0, 0, 228, 0, 0, 0, 0, 0, 0, 0, 11, 22, 261
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/image_picker_debug_page.dart", "Dart", 0, 0, 0, 0, 297, 0, 0, 0, 0, 0, 0, 0, 16, 36, 349
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/ios_photo_selection_debugger.dart", "Dart", 0, 0, 0, 0, 341, 0, 0, 0, 0, 0, 0, 0, 28, 50, 419
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/ios_simulator_test_page.dart", "Dart", 0, 0, 0, 0, 467, 0, 0, 0, 0, 0, 0, 0, 25, 47, 539
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/photo_rendering_debugger.dart", "Dart", 0, 0, 0, 0, 366, 0, 0, 0, 0, 0, 0, 0, 26, 48, 440
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/photo_selection_advanced_diagnostics.dart", "Dart", 0, 0, 0, 0, 439, 0, 0, 0, 0, 0, 0, 0, 48, 74, 561
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/photo_selection_validator.dart", "Dart", 0, 0, 0, 0, 168, 0, 0, 0, 0, 0, 0, 0, 18, 35, 221
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/profanity_filter_manual_test_page.dart", "Dart", 0, 0, 0, 0, 291, 0, 0, 0, 0, 0, 0, 0, 10, 31, 332
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/profanity_filter_test_page.dart", "Dart", 0, 0, 0, 0, 189, 0, 0, 0, 0, 0, 0, 0, 2, 28, 219
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/profile_save_test_page.dart", "Dart", 0, 0, 0, 0, 276, 0, 0, 0, 0, 0, 0, 0, 20, 34, 330
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/radar_test_page.dart", "Dart", 0, 0, 0, 0, 69, 0, 0, 0, 0, 0, 0, 0, 2, 7, 78
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/route_debug_page.dart", "Dart", 0, 0, 0, 0, 283, 0, 0, 0, 0, 0, 0, 0, 10, 15, 308
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/simple_image_picker_test.dart", "Dart", 0, 0, 0, 0, 209, 0, 0, 0, 0, 0, 0, 0, 11, 27, 247
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/simple_photo_test.dart", "Dart", 0, 0, 0, 0, 137, 0, 0, 0, 0, 0, 0, 0, 7, 16, 160
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/simulator_reset_helper.dart", "Dart", 0, 0, 0, 0, 407, 0, 0, 0, 0, 0, 0, 0, 25, 40, 472
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/models/ability_radar_models.dart", "Dart", 0, 0, 0, 0, 222, 0, 0, 0, 0, 0, 0, 0, 44, 65, 331
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/models/ability_radar_models.g.dart", "Dart", 0, 0, 0, 0, 199, 0, 0, 0, 0, 0, 0, 0, 32, 18, 249
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/pages/ability_radar_page.dart", "Dart", 0, 0, 0, 0, 635, 0, 0, 0, 0, 0, 0, 0, 52, 58, 745
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/providers/ability_radar_mock_providers.dart", "Dart", 0, 0, 0, 0, 140, 0, 0, 0, 0, 0, 0, 0, 5, 6, 151
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/providers/ability_radar_providers.dart", "Dart", 0, 0, 0, 0, 303, 0, 0, 0, 0, 0, 0, 0, 21, 57, 381
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/services/ability_radar_service.dart", "Dart", 0, 0, 0, 0, 461, 0, 0, 0, 0, 0, 0, 0, 45, 106, 612
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/services/radar_share_service.dart", "Dart", 0, 0, 0, 0, 344, 0, 0, 0, 0, 0, 0, 0, 59, 71, 474
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/widgets/ability_radar_chart.dart", "Dart", 0, 0, 0, 0, 337, 0, 0, 0, 0, 0, 0, 0, 31, 27, 395
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/widgets/game_style_rank_widget.dart", "Dart", 0, 0, 0, 0, 242, 0, 0, 0, 0, 0, 0, 0, 20, 31, 293
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/widgets/radar_community_share_dialog.dart", "Dart", 0, 0, 0, 0, 411, 0, 0, 0, 0, 0, 0, 0, 24, 33, 468
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/widgets/radar_share_panel.dart", "Dart", 0, 0, 0, 0, 259, 0, 0, 0, 0, 0, 0, 0, 26, 26, 311
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/README.md", "Markdown", 132, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 173
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/data/achievements_data.dart", "Dart", 0, 0, 0, 0, 444, 0, 0, 0, 0, 0, 0, 0, 18, 12, 474
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/achievement.dart", "Dart", 0, 0, 0, 0, 169, 0, 0, 0, 0, 0, 0, 0, 29, 39, 237
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/achievement.g.dart", "Dart", 0, 0, 0, 0, 125, 0, 0, 0, 0, 0, 0, 0, 24, 11, 160
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/badge.dart", "Dart", 0, 0, 0, 0, 230, 0, 0, 0, 0, 0, 0, 0, 50, 69, 349
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/badge.g.dart", "Dart", 0, 0, 0, 0, 184, 0, 0, 0, 0, 0, 0, 0, 40, 17, 241
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/user_level.dart", "Dart", 0, 0, 0, 0, 266, 0, 0, 0, 0, 0, 0, 0, 41, 48, 355
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/user_level.g.dart", "Dart", 0, 0, 0, 0, 91, 0, 0, 0, 0, 0, 0, 0, 21, 11, 123
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/pages/achievement_page.dart", "Dart", 0, 0, 0, 0, 485, 0, 0, 0, 0, 0, 0, 0, 21, 39, 545
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/pages/leaderboard_page.dart", "Dart", 0, 0, 0, 0, 336, 0, 0, 0, 0, 0, 0, 0, 13, 20, 369
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/providers/achievement_provider.dart", "Dart", 0, 0, 0, 0, 224, 0, 0, 0, 0, 0, 0, 0, 38, 44, 306
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/services/achievement_service.dart", "Dart", 0, 0, 0, 0, 332, 0, 0, 0, 0, 0, 0, 0, 38, 66, 436
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/services/achievement_trigger_service.dart", "Dart", 0, 0, 0, 0, 113, 0, 0, 0, 0, 0, 0, 0, 61, 63, 237
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/widgets/achievement_grid.dart", "Dart", 0, 0, 0, 0, 380, 0, 0, 0, 0, 0, 0, 0, 19, 32, 431
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/widgets/achievement_unlock_notification.dart", "Dart", 0, 0, 0, 0, 330, 0, 0, 0, 0, 0, 0, 0, 21, 47, 398
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/widgets/skill_levels_card.dart", "Dart", 0, 0, 0, 0, 323, 0, 0, 0, 0, 0, 0, 0, 15, 42, 380
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/widgets/user_level_card.dart", "Dart", 0, 0, 0, 0, 326, 0, 0, 0, 0, 0, 0, 0, 7, 29, 362
"/Users/<USER>/development/flutter_apps/oneday/lib/features/auth/forgot_password_page.dart", "Dart", 0, 0, 0, 0, 266, 0, 0, 0, 0, 0, 0, 0, 17, 20, 303
"/Users/<USER>/development/flutter_apps/oneday/lib/features/auth/login_page.dart", "Dart", 0, 0, 0, 0, 710, 0, 0, 0, 0, 0, 0, 0, 42, 48, 800
"/Users/<USER>/development/flutter_apps/oneday/lib/features/auth/phone_login_page.dart", "Dart", 0, 0, 0, 0, 351, 0, 0, 0, 0, 0, 0, 0, 23, 25, 399
"/Users/<USER>/development/flutter_apps/oneday/lib/features/auth/providers/phone_login_provider.dart", "Dart", 0, 0, 0, 0, 194, 0, 0, 0, 0, 0, 0, 0, 20, 32, 246
"/Users/<USER>/development/flutter_apps/oneday/lib/features/auth/providers/wechat_login_provider.dart", "Dart", 0, 0, 0, 0, 137, 0, 0, 0, 0, 0, 0, 0, 13, 25, 175
"/Users/<USER>/development/flutter_apps/oneday/lib/features/auth/register_page.dart", "Dart", 0, 0, 0, 0, 474, 0, 0, 0, 0, 0, 0, 0, 27, 31, 532
"/Users/<USER>/development/flutter_apps/oneday/lib/features/auth/reset_password_page.dart", "Dart", 0, 0, 0, 0, 316, 0, 0, 0, 0, 0, 0, 0, 18, 22, 356
"/Users/<USER>/development/flutter_apps/oneday/lib/features/auth/services/sms_service.dart", "Dart", 0, 0, 0, 0, 174, 0, 0, 0, 0, 0, 0, 0, 31, 46, 251
"/Users/<USER>/development/flutter_apps/oneday/lib/features/auth/services/wechat_auth_service.dart", "Dart", 0, 0, 0, 0, 85, 0, 0, 0, 0, 0, 0, 0, 120, 33, 238
"/Users/<USER>/development/flutter_apps/oneday/lib/features/calendar/calendar_page.dart", "Dart", 0, 0, 0, 0, 1094, 0, 0, 0, 0, 0, 0, 0, 89, 77, 1260
"/Users/<USER>/development/flutter_apps/oneday/lib/features/community/article_import_service.dart", "Dart", 0, 0, 0, 0, 244, 0, 0, 0, 0, 0, 0, 0, 29, 43, 316
"/Users/<USER>/development/flutter_apps/oneday/lib/features/community/community_feed_page.dart", "Dart", 0, 0, 0, 0, 1291, 0, 0, 0, 0, 0, 0, 0, 73, 89, 1453
"/Users/<USER>/development/flutter_apps/oneday/lib/features/community/community_post_editor_page.dart", "Dart", 0, 0, 0, 0, 905, 0, 0, 0, 0, 0, 0, 0, 52, 79, 1036
"/Users/<USER>/development/flutter_apps/oneday/lib/features/community/community_storage_service.dart", "Dart", 0, 0, 0, 0, 121, 0, 0, 0, 0, 0, 0, 0, 11, 20, 152
"/Users/<USER>/development/flutter_apps/oneday/lib/features/community/content_report_dialog.dart", "Dart", 0, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 7, 18, 389
"/Users/<USER>/development/flutter_apps/oneday/lib/features/community/content_report_service.dart", "Dart", 0, 0, 0, 0, 347, 0, 0, 0, 0, 0, 0, 0, 33, 57, 437
"/Users/<USER>/development/flutter_apps/oneday/lib/features/community/profanity_filter_service.dart", "Dart", 0, 0, 0, 0, 368, 0, 0, 0, 0, 0, 0, 0, 34, 60, 462
"/Users/<USER>/development/flutter_apps/oneday/lib/features/community/profanity_filter_settings_page.dart", "Dart", 0, 0, 0, 0, 722, 0, 0, 0, 0, 0, 0, 0, 16, 30, 768
"/Users/<USER>/development/flutter_apps/oneday/lib/features/daily_plan/models/daily_plan.dart", "Dart", 0, 0, 0, 0, 151, 0, 0, 0, 0, 0, 0, 0, 29, 34, 214
"/Users/<USER>/development/flutter_apps/oneday/lib/features/daily_plan/models/daily_plan.g.dart", "Dart", 0, 0, 0, 0, 49, 0, 0, 0, 0, 0, 0, 0, 13, 7, 69
"/Users/<USER>/development/flutter_apps/oneday/lib/features/daily_plan/notifiers/daily_plan_notifier.dart", "Dart", 0, 0, 0, 0, 204, 0, 0, 0, 0, 0, 0, 0, 31, 44, 279
"/Users/<USER>/development/flutter_apps/oneday/lib/features/daily_plan/services/daily_plan_storage_service.dart", "Dart", 0, 0, 0, 0, 216, 0, 0, 0, 0, 0, 0, 0, 33, 48, 297
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/action_library_category_manager.dart", "Dart", 0, 0, 0, 0, 312, 0, 0, 0, 0, 0, 0, 0, 24, 33, 369
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/add_to_memory_dialog.dart", "Dart", 0, 0, 0, 0, 182, 0, 0, 0, 0, 0, 0, 0, 10, 17, 209
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/create_action_library_dialog.dart", "Dart", 0, 0, 0, 0, 345, 0, 0, 0, 0, 0, 0, 0, 7, 20, 372
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/create_custom_library_dialog.dart", "Dart", 0, 0, 0, 0, 297, 0, 0, 0, 0, 0, 0, 0, 7, 17, 321
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/custom_action_editor_dialog.dart", "Dart", 0, 0, 0, 0, 535, 0, 0, 0, 0, 0, 0, 0, 19, 39, 593
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/custom_action_library.dart", "Dart", 0, 0, 0, 0, 207, 0, 0, 0, 0, 0, 0, 0, 25, 29, 261
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/custom_action_library_service.dart", "Dart", 0, 0, 0, 0, 258, 0, 0, 0, 0, 0, 0, 0, 33, 49, 340
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/custom_exercise_category.dart", "Dart", 0, 0, 0, 0, 299, 0, 0, 0, 0, 0, 0, 0, 37, 50, 386
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/custom_library_editor_page.dart", "Dart", 0, 0, 0, 0, 714, 0, 0, 0, 0, 0, 0, 0, 42, 54, 810
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/exercise_library_page.dart", "Dart", 0, 0, 0, 0, 4769, 0, 0, 0, 0, 0, 0, 0, 304, 311, 5384
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/exercise_session_page.dart", "Dart", 0, 0, 0, 0, 998, 0, 0, 0, 0, 0, 0, 0, 71, 83, 1152
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/focused_training_page.dart", "Dart", 0, 0, 0, 0, 461, 0, 0, 0, 0, 0, 0, 0, 18, 41, 520
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/manage_custom_libraries_dialog.dart", "Dart", 0, 0, 0, 0, 2005, 0, 0, 0, 0, 0, 0, 0, 103, 122, 2230
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/pao_integration_service.dart", "Dart", 0, 0, 0, 0, 206, 0, 0, 0, 0, 0, 0, 0, 32, 45, 283
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/providers/exercise_providers.dart", "Dart", 0, 0, 0, 0, 322, 0, 0, 0, 0, 0, 0, 0, 32, 45, 399
"/Users/<USER>/development/flutter_apps/oneday/lib/features/help_feedback/help_feedback_page.dart", "Dart", 0, 0, 0, 0, 708, 0, 0, 0, 0, 0, 0, 0, 39, 53, 800
"/Users/<USER>/development/flutter_apps/oneday/lib/features/help_feedback/user_guide_page.dart", "Dart", 0, 0, 0, 0, 352, 0, 0, 0, 0, 0, 0, 0, 11, 32, 395
"/Users/<USER>/development/flutter_apps/oneday/lib/features/home/<USER>", "Dart", 0, 0, 0, 0, 1254, 0, 0, 0, 0, 0, 0, 0, 98, 83, 1435
"/Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/learning_report_page.dart", "Dart", 0, 0, 0, 0, 795, 0, 0, 0, 0, 0, 0, 0, 40, 58, 893
"/Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/models/learning_report_models.dart", "Dart", 0, 0, 0, 0, 203, 0, 0, 0, 0, 0, 0, 0, 61, 77, 341
"/Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/models/learning_report_models.g.dart", "Dart", 0, 0, 0, 0, 301, 0, 0, 0, 0, 0, 0, 0, 51, 25, 377
"/Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/providers/learning_report_providers.dart", "Dart", 0, 0, 0, 0, 148, 0, 0, 0, 0, 0, 0, 0, 32, 40, 220
"/Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/services/learning_report_export_service.dart", "Dart", 0, 0, 0, 0, 556, 0, 0, 0, 0, 0, 0, 0, 116, 57, 729
"/Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/services/learning_report_service.dart", "Dart", 0, 0, 0, 0, 474, 0, 0, 0, 0, 0, 0, 0, 61, 100, 635
"/Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/widgets/chart_widgets.dart", "Dart", 0, 0, 0, 0, 858, 0, 0, 0, 0, 0, 0, 0, 36, 48, 942
"/Users/<USER>/development/flutter_apps/oneday/lib/features/main/main_container_page.dart", "Dart", 0, 0, 0, 0, 105, 0, 0, 0, 0, 0, 0, 0, 7, 11, 123
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/palace_manager_page.dart", "Dart", 0, 0, 0, 0, 5131, 0, 0, 0, 0, 0, 0, 0, 390, 460, 5981
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/providers/memory_palace_provider.dart", "Dart", 0, 0, 0, 0, 191, 0, 0, 0, 0, 0, 0, 0, 18, 33, 242
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page.dart", "Dart", 0, 0, 0, 0, 3943, 0, 0, 0, 0, 0, 0, 0, 629, 648, 5220
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page_copy.dart", "Dart", 0, 0, 0, 0, 1596, 0, 0, 0, 0, 0, 0, 0, 167, 199, 1962
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/utils/anchor_data_migration.dart", "Dart", 0, 0, 0, 0, 214, 0, 0, 0, 0, 0, 0, 0, 30, 51, 295
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/utils/image_coordinate_system.dart", "Dart", 0, 0, 0, 0, 195, 0, 0, 0, 0, 0, 0, 0, 30, 42, 267
"/Users/<USER>/development/flutter_apps/oneday/lib/features/onboarding/notion_style_onboarding_page.dart", "Dart", 0, 0, 0, 0, 246, 0, 0, 0, 0, 0, 0, 0, 18, 25, 289
"/Users/<USER>/development/flutter_apps/oneday/lib/features/onboarding/onboarding_page.dart", "Dart", 0, 0, 0, 0, 770, 0, 0, 0, 0, 0, 0, 0, 59, 76, 905
"/Users/<USER>/development/flutter_apps/oneday/lib/features/photo_album/photo_album_creator_page.dart", "Dart", 0, 0, 0, 0, 1641, 0, 0, 0, 0, 0, 0, 0, 115, 152, 1908
"/Users/<USER>/development/flutter_apps/oneday/lib/features/profile/models/user_profile.dart", "Dart", 0, 0, 0, 0, 84, 0, 0, 0, 0, 0, 0, 0, 14, 21, 119
"/Users/<USER>/development/flutter_apps/oneday/lib/features/profile/models/user_profile.g.dart", "Dart", 0, 0, 0, 0, 31, 0, 0, 0, 0, 0, 0, 0, 11, 6, 48
"/Users/<USER>/development/flutter_apps/oneday/lib/features/profile/pages/profile_edit_page.dart", "Dart", 0, 0, 0, 0, 901, 0, 0, 0, 0, 0, 0, 0, 52, 79, 1032
"/Users/<USER>/development/flutter_apps/oneday/lib/features/profile/profile_page.dart", "Dart", 0, 0, 0, 0, 825, 0, 0, 0, 0, 0, 0, 0, 44, 70, 939
"/Users/<USER>/development/flutter_apps/oneday/lib/features/profile/providers/user_profile_provider.dart", "Dart", 0, 0, 0, 0, 189, 0, 0, 0, 0, 0, 0, 0, 20, 35, 244
"/Users/<USER>/development/flutter_apps/oneday/lib/features/profile/services/user_profile_service.dart", "Dart", 0, 0, 0, 0, 183, 0, 0, 0, 0, 0, 0, 0, 23, 38, 244
"/Users/<USER>/development/flutter_apps/oneday/lib/features/reflection/reflection_log_page.dart", "Dart", 0, 0, 0, 0, 1327, 0, 0, 0, 0, 0, 0, 0, 66, 136, 1529
"/Users/<USER>/development/flutter_apps/oneday/lib/features/settings/settings_page.dart", "Dart", 0, 0, 0, 0, 793, 0, 0, 0, 0, 0, 0, 0, 57, 69, 919
"/Users/<USER>/development/flutter_apps/oneday/lib/features/splash/splash_page.dart", "Dart", 0, 0, 0, 0, 177, 0, 0, 0, 0, 0, 0, 0, 12, 23, 212
"/Users/<USER>/development/flutter_apps/oneday/lib/features/study_time/models/study_time_models.dart", "Dart", 0, 0, 0, 0, 264, 0, 0, 0, 0, 0, 0, 0, 58, 68, 390
"/Users/<USER>/development/flutter_apps/oneday/lib/features/study_time/models/study_time_models.g.dart", "Dart", 0, 0, 0, 0, 175, 0, 0, 0, 0, 0, 0, 0, 34, 12, 221
"/Users/<USER>/development/flutter_apps/oneday/lib/features/study_time/providers/study_time_providers.dart", "Dart", 0, 0, 0, 0, 310, 0, 0, 0, 0, 0, 0, 0, 26, 51, 387
"/Users/<USER>/development/flutter_apps/oneday/lib/features/study_time/services/study_time_statistics_service.dart", "Dart", 0, 0, 0, 0, 348, 0, 0, 0, 0, 0, 0, 0, 65, 86, 499
"/Users/<USER>/development/flutter_apps/oneday/lib/features/study_time/test_data_sync_page.dart", "Dart", 0, 0, 0, 0, 253, 0, 0, 0, 0, 0, 0, 0, 11, 17, 281
"/Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/managers/task_category_manager.dart", "Dart", 0, 0, 0, 0, 311, 0, 0, 0, 0, 0, 0, 0, 47, 54, 412
"/Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/models/timebox_models.dart", "Dart", 0, 0, 0, 0, 355, 0, 0, 0, 0, 0, 0, 0, 58, 72, 485
"/Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/models/timebox_models.g.dart", "Dart", 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 15, 8, 94
"/Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/pages/task_category_management_page.dart", "Dart", 0, 0, 0, 0, 489, 0, 0, 0, 0, 0, 0, 0, 14, 35, 538
"/Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/providers/timebox_provider.dart", "Dart", 0, 0, 0, 0, 261, 0, 0, 0, 0, 0, 0, 0, 27, 32, 320
"/Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/timebox_list_page.dart", "Dart", 0, 0, 0, 0, 3495, 0, 0, 0, 0, 0, 0, 0, 261, 245, 4001
"/Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/widgets/study_session_completion_dialog.dart", "Dart", 0, 0, 0, 0, 405, 0, 0, 0, 0, 0, 0, 0, 6, 21, 432
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/create_vocabulary_page.dart", "Dart", 0, 0, 0, 0, 452, 0, 0, 0, 0, 0, 0, 0, 11, 39, 502
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/custom_vocabulary_manager_page.dart", "Dart", 0, 0, 0, 0, 518, 0, 0, 0, 0, 0, 0, 0, 30, 38, 586
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/fsrs_algorithm.dart", "Dart", 0, 0, 0, 0, 223, 0, 0, 0, 0, 0, 0, 0, 44, 40, 307
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/fsrs_service.dart", "Dart", 0, 0, 0, 0, 316, 0, 0, 0, 0, 0, 0, 0, 115, 70, 501
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/graduate_vocabulary_manager_page.dart", "Dart", 0, 0, 0, 0, 598, 0, 0, 0, 0, 0, 0, 0, 37, 49, 684
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/providers/vocabulary_providers.dart", "Dart", 0, 0, 0, 0, 263, 0, 0, 0, 0, 0, 0, 0, 27, 43, 333
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_cache_manager.dart", "Dart", 0, 0, 0, 0, 248, 0, 0, 0, 0, 0, 0, 0, 33, 64, 345
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_category_page.dart", "Dart", 0, 0, 0, 0, 1358, 0, 0, 0, 0, 0, 0, 0, 71, 97, 1526
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_learning_service.dart", "Dart", 0, 0, 0, 0, 232, 0, 0, 0, 0, 0, 0, 0, 28, 47, 307
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_manager_page.dart", "Dart", 0, 0, 0, 0, 1002, 0, 0, 0, 0, 0, 0, 0, 42, 75, 1119
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_page.dart", "Dart", 0, 0, 0, 0, 188, 0, 0, 0, 0, 0, 0, 0, 2, 13, 203
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_service.dart", "Dart", 0, 0, 0, 0, 636, 0, 0, 0, 0, 0, 0, 0, 83, 129, 848
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_statistics_page.dart", "Dart", 0, 0, 0, 0, 845, 0, 0, 0, 0, 0, 0, 0, 29, 83, 957
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/word_meaning_service.dart", "Dart", 0, 0, 0, 0, 161, 0, 0, 0, 0, 0, 0, 0, 17, 32, 210
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/word_model.dart", "Dart", 0, 0, 0, 0, 590, 0, 0, 0, 0, 0, 0, 0, 21, 43, 654
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/word_root_service.dart", "Dart", 0, 0, 0, 0, 188, 0, 0, 0, 0, 0, 0, 0, 28, 37, 253
"/Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/models/item_effect_models.dart", "Dart", 0, 0, 0, 0, 220, 0, 0, 0, 0, 0, 0, 0, 22, 28, 270
"/Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/models/wage_transaction.dart", "Dart", 0, 0, 0, 0, 125, 0, 0, 0, 0, 0, 0, 0, 5, 13, 143
"/Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/providers/item_effect_provider.dart", "Dart", 0, 0, 0, 0, 42, 0, 0, 0, 0, 0, 0, 0, 9, 11, 62
"/Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/services/item_effect_service.dart", "Dart", 0, 0, 0, 0, 232, 0, 0, 0, 0, 0, 0, 0, 42, 42, 316
"/Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/services/item_timebox_integration_service.dart", "Dart", 0, 0, 0, 0, 176, 0, 0, 0, 0, 0, 0, 0, 47, 35, 258
"/Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/services/premium_article_service.dart", "Dart", 0, 0, 0, 0, 259, 0, 0, 0, 0, 0, 0, 0, 35, 51, 345
"/Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/services/wage_service.dart", "Dart", 0, 0, 0, 0, 207, 0, 0, 0, 0, 0, 0, 0, 18, 38, 263
"/Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/store_page.dart", "Dart", 0, 0, 0, 0, 1930, 0, 0, 0, 0, 0, 0, 0, 97, 121, 2148
"/Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/wage_wallet_page.dart", "Dart", 0, 0, 0, 0, 780, 0, 0, 0, 0, 0, 0, 0, 34, 52, 866
"/Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/widgets/active_effects_widget.dart", "Dart", 0, 0, 0, 0, 303, 0, 0, 0, 0, 0, 0, 0, 2, 17, 322
"/Users/<USER>/development/flutter_apps/oneday/lib/features/widget/models/widget_models.dart", "Dart", 0, 0, 0, 0, 152, 0, 0, 0, 0, 0, 0, 0, 15, 22, 189
"/Users/<USER>/development/flutter_apps/oneday/lib/features/widget/services/memory_palace_integration_service.dart", "Dart", 0, 0, 0, 0, 350, 0, 0, 0, 0, 0, 0, 0, 38, 47, 435
"/Users/<USER>/development/flutter_apps/oneday/lib/features/widget/services/widget_service.dart", "Dart", 0, 0, 0, 0, 126, 0, 0, 0, 0, 0, 0, 0, 88, 32, 246
"/Users/<USER>/development/flutter_apps/oneday/lib/features/widget/utils/error_handler.dart", "Dart", 0, 0, 0, 0, 360, 0, 0, 0, 0, 0, 0, 0, 15, 24, 399
"/Users/<USER>/development/flutter_apps/oneday/lib/l10n/app_localizations.dart", "Dart", 0, 0, 0, 0, 129, 0, 0, 0, 0, 0, 0, 0, 360, 90, 579
"/Users/<USER>/development/flutter_apps/oneday/lib/l10n/app_localizations_en.dart", "Dart", 0, 0, 0, 0, 175, 0, 0, 0, 0, 0, 0, 0, 3, 77, 255
"/Users/<USER>/development/flutter_apps/oneday/lib/l10n/app_localizations_zh.dart", "Dart", 0, 0, 0, 0, 165, 0, 0, 0, 0, 0, 0, 0, 3, 77, 245
"/Users/<USER>/development/flutter_apps/oneday/lib/main.dart", "Dart", 0, 0, 0, 0, 258, 0, 0, 0, 0, 0, 0, 0, 25, 13, 296
"/Users/<USER>/development/flutter_apps/oneday/lib/providers/navigation_provider.dart", "Dart", 0, 0, 0, 0, 51, 0, 0, 0, 0, 0, 0, 0, 6, 11, 68
"/Users/<USER>/development/flutter_apps/oneday/lib/router/app_router.dart", "Dart", 0, 0, 0, 0, 427, 0, 0, 0, 0, 0, 0, 0, 45, 55, 527
"/Users/<USER>/development/flutter_apps/oneday/lib/services/app_initialization_service.dart", "Dart", 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 17, 19, 94
"/Users/<USER>/development/flutter_apps/oneday/lib/services/first_time_service.dart", "Dart", 0, 0, 0, 0, 44, 0, 0, 0, 0, 0, 0, 0, 16, 13, 73
"/Users/<USER>/development/flutter_apps/oneday/lib/services/floating_timer_service.dart", "Dart", 0, 0, 0, 0, 487, 0, 0, 0, 0, 0, 0, 0, 77, 79, 643
"/Users/<USER>/development/flutter_apps/oneday/lib/services/global_timer_service.dart", "Dart", 0, 0, 0, 0, 366, 0, 0, 0, 0, 0, 0, 0, 89, 99, 554
"/Users/<USER>/development/flutter_apps/oneday/lib/services/locale_service.dart", "Dart", 0, 0, 0, 0, 84, 0, 0, 0, 0, 0, 0, 0, 23, 17, 124
"/Users/<USER>/development/flutter_apps/oneday/lib/services/providers/study_session_completion_provider.dart", "Dart", 0, 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 2, 4, 25
"/Users/<USER>/development/flutter_apps/oneday/lib/services/study_session_completion_service.dart", "Dart", 0, 0, 0, 0, 185, 0, 0, 0, 0, 0, 0, 0, 37, 45, 267
"/Users/<USER>/development/flutter_apps/oneday/lib/services/system_overlay_permission.dart", "Dart", 0, 0, 0, 0, 256, 0, 0, 0, 0, 0, 0, 0, 26, 24, 306
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/config/image_compression_config.dart", "Dart", 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 20, 18, 105
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/services/enhanced_share_service.dart", "Dart", 0, 0, 0, 0, 160, 0, 0, 0, 0, 0, 0, 0, 27, 37, 224
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/image_compression_utils.dart", "Dart", 0, 0, 0, 0, 213, 0, 0, 0, 0, 0, 0, 0, 41, 47, 301
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/image_watermark_utils.dart", "Dart", 0, 0, 0, 0, 285, 0, 0, 0, 0, 0, 0, 0, 62, 59, 406
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/silent_refresh_utils.dart", "Dart", 0, 0, 0, 0, 104, 0, 0, 0, 0, 0, 0, 0, 35, 21, 160
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/simple_image_test.dart", "Dart", 0, 0, 0, 0, 82, 0, 0, 0, 0, 0, 0, 0, 14, 26, 122
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/ui_utils.dart", "Dart", 0, 0, 0, 0, 77, 0, 0, 0, 0, 0, 0, 0, 19, 9, 105
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/widgets/icons/wechat_icon.dart", "Dart", 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 8, 14, 84
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/widgets/oneday_logo.dart", "Dart", 0, 0, 0, 0, 204, 0, 0, 0, 0, 0, 0, 0, 17, 43, 264
"/Users/<USER>/development/flutter_apps/oneday/lib/utils/category_cleanup_helper.dart", "Dart", 0, 0, 0, 0, 96, 0, 0, 0, 0, 0, 0, 0, 10, 20, 126
"/Users/<USER>/development/flutter_apps/oneday/lib/utils/image_rendering_fix.dart", "Dart", 0, 0, 0, 0, 135, 0, 0, 0, 0, 0, 0, 0, 10, 10, 155
"/Users/<USER>/development/flutter_apps/oneday/lib/utils/ios_simulator_detector.dart", "Dart", 0, 0, 0, 0, 157, 0, 0, 0, 0, 0, 0, 0, 20, 30, 207
"/Users/<USER>/development/flutter_apps/oneday/lib/utils/safe_area_helper.dart", "Dart", 0, 0, 0, 0, 475, 0, 0, 0, 0, 0, 0, 0, 66, 65, 606
"/Users/<USER>/development/flutter_apps/oneday/linux/CMakeLists.txt", "CMake", 0, 104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 129
"/Users/<USER>/development/flutter_apps/oneday/linux/flutter/CMakeLists.txt", "CMake", 0, 79, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 89
"/Users/<USER>/development/flutter_apps/oneday/linux/flutter/generated_plugin_registrant.cc", "C++", 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 5, 20
"/Users/<USER>/development/flutter_apps/oneday/linux/flutter/generated_plugin_registrant.h", "C++", 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 6, 16
"/Users/<USER>/development/flutter_apps/oneday/linux/flutter/generated_plugins.cmake", "CMake", 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"/Users/<USER>/development/flutter_apps/oneday/linux/runner/CMakeLists.txt", "CMake", 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 27
"/Users/<USER>/development/flutter_apps/oneday/linux/runner/main.cc", "C++", 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 7
"/Users/<USER>/development/flutter_apps/oneday/linux/runner/my_application.cc", "C++", 0, 0, 83, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 27, 131
"/Users/<USER>/development/flutter_apps/oneday/linux/runner/my_application.h", "C++", 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 5, 19
"/Users/<USER>/development/flutter_apps/oneday/macos/Flutter/GeneratedPluginRegistrant.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 3, 4, 27
"/Users/<USER>/development/flutter_apps/oneday/macos/Podfile", "Ruby", 0, 0, 0, 0, 0, 0, 32, 0, 0, 0, 0, 0, 1, 10, 43
"/Users/<USER>/development/flutter_apps/oneday/macos/Runner/AppDelegate.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 0, 0, 0, 3, 14
"/Users/<USER>/development/flutter_apps/oneday/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 68
"/Users/<USER>/development/flutter_apps/oneday/macos/Runner/Base.lproj/MainMenu.xib", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 343, 0, 0, 1, 344
"/Users/<USER>/development/flutter_apps/oneday/macos/Runner/MainFlutterWindow.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 4, 16
"/Users/<USER>/development/flutter_apps/oneday/macos/RunnerTests/RunnerTests.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 0, 2, 4, 13
"/Users/<USER>/development/flutter_apps/oneday/pubspec.yaml", "YAML", 0, 0, 0, 75, 0, 0, 0, 0, 0, 0, 0, 0, 60, 35, 170
"/Users/<USER>/development/flutter_apps/oneday/scripts/clean_category_data.dart", "Dart", 0, 0, 0, 0, 35, 0, 0, 0, 0, 0, 0, 0, 8, 6, 49
"/Users/<USER>/development/flutter_apps/oneday/scripts/clear_wage_statistics.dart", "Dart", 0, 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 4, 2, 31
"/Users/<USER>/development/flutter_apps/oneday/scripts/transform_vocabulary.py", "Python", 0, 0, 0, 0, 0, 152, 0, 0, 0, 0, 0, 0, 26, 32, 210
"/Users/<USER>/development/flutter_apps/oneday/scripts/verify_navigation_fix.dart", "Dart", 0, 0, 0, 0, 174, 0, 0, 0, 0, 0, 0, 0, 20, 44, 238
"/Users/<USER>/development/flutter_apps/oneday/scripts/verify_radar_fix.dart", "Dart", 0, 0, 0, 0, 111, 0, 0, 0, 0, 0, 0, 0, 8, 24, 143
"/Users/<USER>/development/flutter_apps/oneday/tempPrompts.md", "Markdown", 220, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 89, 309
"/Users/<USER>/development/flutter_apps/oneday/test/achievement_unlock_navigation_test.dart", "Dart", 0, 0, 0, 0, 79, 0, 0, 0, 0, 0, 0, 0, 12, 13, 104
"/Users/<USER>/development/flutter_apps/oneday/test/achievement_wage_integration_test.dart", "Dart", 0, 0, 0, 0, 78, 0, 0, 0, 0, 0, 0, 0, 15, 21, 114
"/Users/<USER>/development/flutter_apps/oneday/test/action_library_category_test.dart", "Dart", 0, 0, 0, 0, 36, 0, 0, 0, 0, 0, 0, 0, 2, 9, 47
"/Users/<USER>/development/flutter_apps/oneday/test/add_child_category_test.dart", "Dart", 0, 0, 0, 0, 146, 0, 0, 0, 0, 0, 0, 0, 30, 48, 224
"/Users/<USER>/development/flutter_apps/oneday/test/bubble_offset_verification.dart", "Dart", 0, 0, 0, 0, 74, 0, 0, 0, 0, 0, 0, 0, 11, 23, 108
"/Users/<USER>/development/flutter_apps/oneday/test/bubble_positioning_analysis.dart", "Dart", 0, 0, 0, 0, 114, 0, 0, 0, 0, 0, 0, 0, 9, 26, 149
"/Users/<USER>/development/flutter_apps/oneday/test/calendar_task_display_test.dart", "Dart", 0, 0, 0, 0, 260, 0, 0, 0, 0, 0, 0, 0, 19, 36, 315
"/Users/<USER>/development/flutter_apps/oneday/test/category_edit_fix_test.dart", "Dart", 0, 0, 0, 0, 104, 0, 0, 0, 0, 0, 0, 0, 15, 21, 140
"/Users/<USER>/development/flutter_apps/oneday/test/category_edit_integration_test.dart", "Dart", 0, 0, 0, 0, 108, 0, 0, 0, 0, 0, 0, 0, 29, 41, 178
"/Users/<USER>/development/flutter_apps/oneday/test/category_edit_state_test.dart", "Dart", 0, 0, 0, 0, 151, 0, 0, 0, 0, 0, 0, 0, 40, 55, 246
"/Users/<USER>/development/flutter_apps/oneday/test/category_popup_menu_test.dart", "Dart", 0, 0, 0, 0, 157, 0, 0, 0, 0, 0, 0, 0, 38, 52, 247
"/Users/<USER>/development/flutter_apps/oneday/test/category_removal_test.dart", "Dart", 0, 0, 0, 0, 151, 0, 0, 0, 0, 0, 0, 0, 27, 32, 210
"/Users/<USER>/development/flutter_apps/oneday/test/category_sidebar_edit_test.dart", "Dart", 0, 0, 0, 0, 177, 0, 0, 0, 0, 0, 0, 0, 45, 60, 282
"/Users/<USER>/development/flutter_apps/oneday/test/category_sidebar_test.dart", "Dart", 0, 0, 0, 0, 118, 0, 0, 0, 0, 0, 0, 0, 20, 34, 172
"/Users/<USER>/development/flutter_apps/oneday/test/community_storage_test.dart", "Dart", 0, 0, 0, 0, 105, 0, 0, 0, 0, 0, 0, 0, 13, 17, 135
"/Users/<USER>/development/flutter_apps/oneday/test/context_aware_album_creation_test.dart", "Dart", 0, 0, 0, 0, 97, 0, 0, 0, 0, 0, 0, 0, 6, 20, 123
"/Users/<USER>/development/flutter_apps/oneday/test/coordinate_system_logic_test.dart", "Dart", 0, 0, 0, 0, 174, 0, 0, 0, 0, 0, 0, 0, 32, 46, 252
"/Users/<USER>/development/flutter_apps/oneday/test/create_album_success_message_test.dart", "Dart", 0, 0, 0, 0, 172, 0, 0, 0, 0, 0, 0, 0, 20, 27, 219
"/Users/<USER>/development/flutter_apps/oneday/test/custom_action_display_test.dart", "Dart", 0, 0, 0, 0, 78, 0, 0, 0, 0, 0, 0, 0, 9, 10, 97
"/Users/<USER>/development/flutter_apps/oneday/test/custom_action_editor_test.dart", "Dart", 0, 0, 0, 0, 66, 0, 0, 0, 0, 0, 0, 0, 14, 14, 94
"/Users/<USER>/development/flutter_apps/oneday/test/custom_library_navigation_test.dart", "Dart", 0, 0, 0, 0, 119, 0, 0, 0, 0, 0, 0, 0, 22, 27, 168
"/Users/<USER>/development/flutter_apps/oneday/test/custom_library_navigation_unit_test.dart", "Dart", 0, 0, 0, 0, 50, 0, 0, 0, 0, 0, 0, 0, 15, 16, 81
"/Users/<USER>/development/flutter_apps/oneday/test/custom_library_section_header_test.dart", "Dart", 0, 0, 0, 0, 92, 0, 0, 0, 0, 0, 0, 0, 25, 38, 155
"/Users/<USER>/development/flutter_apps/oneday/test/date_format_test.dart", "Dart", 0, 0, 0, 0, 44, 0, 0, 0, 0, 0, 0, 0, 5, 13, 62
"/Users/<USER>/development/flutter_apps/oneday/test/default_category_edit_test.dart", "Dart", 0, 0, 0, 0, 132, 0, 0, 0, 0, 0, 0, 0, 31, 43, 206
"/Users/<USER>/development/flutter_apps/oneday/test/developer_tools_test.dart", "Dart", 0, 0, 0, 0, 132, 0, 0, 0, 0, 0, 0, 0, 12, 29, 173
"/Users/<USER>/development/flutter_apps/oneday/test/exercise_library_sidebar_test.dart", "Dart", 0, 0, 0, 0, 76, 0, 0, 0, 0, 0, 0, 0, 15, 23, 114
"/Users/<USER>/development/flutter_apps/oneday/test/exercise_library_ui_test.dart", "Dart", 0, 0, 0, 0, 156, 0, 0, 0, 0, 0, 0, 0, 31, 55, 242
"/Users/<USER>/development/flutter_apps/oneday/test/features/auth/register_page_test.dart", "Dart", 0, 0, 0, 0, 41, 0, 0, 0, 0, 0, 0, 0, 10, 15, 66
"/Users/<USER>/development/flutter_apps/oneday/test/features/exercise/category_integration_test.dart", "Dart", 0, 0, 0, 0, 128, 0, 0, 0, 0, 0, 0, 0, 22, 27, 177
"/Users/<USER>/development/flutter_apps/oneday/test/features/exercise/custom_action_library_test.dart", "Dart", 0, 0, 0, 0, 155, 0, 0, 0, 0, 0, 0, 0, 32, 43, 230
"/Users/<USER>/development/flutter_apps/oneday/test/features/exercise/custom_action_library_ui_test.dart", "Dart", 0, 0, 0, 0, 178, 0, 0, 0, 0, 0, 0, 0, 27, 39, 244
"/Users/<USER>/development/flutter_apps/oneday/test/features/exercise/custom_category_test.dart", "Dart", 0, 0, 0, 0, 113, 0, 0, 0, 0, 0, 0, 0, 21, 24, 158
"/Users/<USER>/development/flutter_apps/oneday/test/features/exercise/exercise_library_page_import_test.dart", "Dart", 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 5, 6, 32
"/Users/<USER>/development/flutter_apps/oneday/test/features/exercise/exercise_library_share_test.dart", "Dart", 0, 0, 0, 0, 250, 0, 0, 0, 0, 0, 0, 0, 26, 38, 314
"/Users/<USER>/development/flutter_apps/oneday/test/features/help_feedback/help_feedback_page_test.dart", "Dart", 0, 0, 0, 0, 88, 0, 0, 0, 0, 0, 0, 0, 17, 33, 138
"/Users/<USER>/development/flutter_apps/oneday/test/features/profile/image_cropper_test.dart", "Dart", 0, 0, 0, 0, 202, 0, 0, 0, 0, 0, 0, 0, 10, 32, 244
"/Users/<USER>/development/flutter_apps/oneday/test/features/profile/image_cropper_test.mocks.dart", "Dart", 0, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 7, 67
"/Users/<USER>/development/flutter_apps/oneday/test/features/profile/profile_data_sync_test.dart", "Dart", 0, 0, 0, 0, 165, 0, 0, 0, 0, 0, 0, 0, 21, 38, 224
"/Users/<USER>/development/flutter_apps/oneday/test/features/profile/user_profile_test.dart", "Dart", 0, 0, 0, 0, 191, 0, 0, 0, 0, 0, 0, 0, 10, 43, 244
"/Users/<USER>/development/flutter_apps/oneday/test/features/study_time/study_time_statistics_test.dart", "Dart", 0, 0, 0, 0, 264, 0, 0, 0, 0, 0, 0, 0, 13, 38, 315
"/Users/<USER>/development/flutter_apps/oneday/test/features/time_box/action_library_selector_overflow_test.dart", "Dart", 0, 0, 0, 0, 301, 0, 0, 0, 0, 0, 0, 0, 30, 49, 380
"/Users/<USER>/development/flutter_apps/oneday/test/features/time_box/timebox_ui_test.dart", "Dart", 0, 0, 0, 0, 125, 0, 0, 0, 0, 0, 0, 0, 11, 18, 154
"/Users/<USER>/development/flutter_apps/oneday/test/features/ui/color_consistency_test.dart", "Dart", 0, 0, 0, 0, 222, 0, 0, 0, 0, 0, 0, 0, 19, 30, 271
"/Users/<USER>/development/flutter_apps/oneday/test/features/ui/dialog_background_simple_test.dart", "Dart", 0, 0, 0, 0, 198, 0, 0, 0, 0, 0, 0, 0, 17, 23, 238
"/Users/<USER>/development/flutter_apps/oneday/test/features/ui/dialog_background_test.dart", "Dart", 0, 0, 0, 0, 228, 0, 0, 0, 0, 0, 0, 0, 23, 32, 283
"/Users/<USER>/development/flutter_apps/oneday/test/features/ui/dropdown_background_test.dart", "Dart", 0, 0, 0, 0, 352, 0, 0, 0, 0, 0, 0, 0, 25, 25, 402
"/Users/<USER>/development/flutter_apps/oneday/test/features/ui/dropdown_pure_white_test.dart", "Dart", 0, 0, 0, 0, 261, 0, 0, 0, 0, 0, 0, 0, 15, 22, 298
"/Users/<USER>/development/flutter_apps/oneday/test/features/ui/final_verification_test.dart", "Dart", 0, 0, 0, 0, 233, 0, 0, 0, 0, 0, 0, 0, 24, 29, 286
"/Users/<USER>/development/flutter_apps/oneday/test/features/ui/material3_surface_tinting_fix_test.dart", "Dart", 0, 0, 0, 0, 260, 0, 0, 0, 0, 0, 0, 0, 16, 23, 299
"/Users/<USER>/development/flutter_apps/oneday/test/features/vocabulary/word_detail_dialog_test.dart", "Dart", 0, 0, 0, 0, 300, 0, 0, 0, 0, 0, 0, 0, 22, 29, 351
"/Users/<USER>/development/flutter_apps/oneday/test/final_category_edit_test.dart", "Dart", 0, 0, 0, 0, 106, 0, 0, 0, 0, 0, 0, 0, 36, 43, 185
"/Users/<USER>/development/flutter_apps/oneday/test/graduate_vocabulary_manager_test.dart", "Dart", 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 6, 20, 84
"/Users/<USER>/development/flutter_apps/oneday/test/image_compression_quality_test.dart", "Dart", 0, 0, 0, 0, 97, 0, 0, 0, 0, 0, 0, 0, 14, 25, 136
"/Users/<USER>/development/flutter_apps/oneday/test/integration/study_session_completion_integration_test.dart", "Dart", 0, 0, 0, 0, 103, 0, 0, 0, 0, 0, 0, 0, 52, 53, 208
"/Users/<USER>/development/flutter_apps/oneday/test/integration_test.dart", "Dart", 0, 0, 0, 0, 89, 0, 0, 0, 0, 0, 0, 0, 27, 39, 155
"/Users/<USER>/development/flutter_apps/oneday/test/ipad_calendar_overflow_test.dart", "Dart", 0, 0, 0, 0, 119, 0, 0, 0, 0, 0, 0, 0, 21, 38, 178
"/Users/<USER>/development/flutter_apps/oneday/test/learning_efficiency_metrics_test.dart", "Dart", 0, 0, 0, 0, 145, 0, 0, 0, 0, 0, 0, 0, 10, 13, 168
"/Users/<USER>/development/flutter_apps/oneday/test/manage_custom_libraries_dialog_test.dart", "Dart", 0, 0, 0, 0, 65, 0, 0, 0, 0, 0, 0, 0, 9, 18, 92
"/Users/<USER>/development/flutter_apps/oneday/test/memory_palace_card_test.dart", "Dart", 0, 0, 0, 0, 200, 0, 0, 0, 0, 0, 0, 0, 13, 29, 242
"/Users/<USER>/development/flutter_apps/oneday/test/memory_palace_persistence_test.dart", "Dart", 0, 0, 0, 0, 166, 0, 0, 0, 0, 0, 0, 0, 14, 25, 205
"/Users/<USER>/development/flutter_apps/oneday/test/navigation_bottom_bar_test.dart", "Dart", 0, 0, 0, 0, 164, 0, 0, 0, 0, 0, 0, 0, 37, 50, 251
"/Users/<USER>/development/flutter_apps/oneday/test/onboarding_web_navigation_test.dart", "Dart", 0, 0, 0, 0, 30, 0, 0, 0, 0, 0, 0, 0, 9, 11, 50
"/Users/<USER>/development/flutter_apps/oneday/test/pdf_color_export_test.dart", "Dart", 0, 0, 0, 0, 87, 0, 0, 0, 0, 0, 0, 0, 12, 14, 113
"/Users/<USER>/development/flutter_apps/oneday/test/photo_album_creator_test.dart", "Dart", 0, 0, 0, 0, 81, 0, 0, 0, 0, 0, 0, 0, 2, 17, 100
"/Users/<USER>/development/flutter_apps/oneday/test/photo_album_dialog_layout_test.dart", "Dart", 0, 0, 0, 0, 75, 0, 0, 0, 0, 0, 0, 0, 24, 31, 130
"/Users/<USER>/development/flutter_apps/oneday/test/photo_album_dialog_test.dart", "Dart", 0, 0, 0, 0, 96, 0, 0, 0, 0, 0, 0, 0, 21, 28, 145
"/Users/<USER>/development/flutter_apps/oneday/test/photo_album_position_fix_test.dart", "Dart", 0, 0, 0, 0, 32, 0, 0, 0, 0, 0, 0, 0, 7, 11, 50
"/Users/<USER>/development/flutter_apps/oneday/test/photo_selection_order_test.dart", "Dart", 0, 0, 0, 0, 78, 0, 0, 0, 0, 0, 0, 0, 14, 21, 113
"/Users/<USER>/development/flutter_apps/oneday/test/photo_selection_unit_test.dart", "Dart", 0, 0, 0, 0, 66, 0, 0, 0, 0, 0, 0, 0, 8, 12, 86
"/Users/<USER>/development/flutter_apps/oneday/test/premium_article_service_test.dart", "Dart", 0, 0, 0, 0, 114, 0, 0, 0, 0, 0, 0, 0, 10, 30, 154
"/Users/<USER>/development/flutter_apps/oneday/test/profanity_filter_integration_test.dart", "Dart", 0, 0, 0, 0, 94, 0, 0, 0, 0, 0, 0, 0, 11, 25, 130
"/Users/<USER>/development/flutter_apps/oneday/test/profile_avatar_click_test.dart", "Dart", 0, 0, 0, 0, 137, 0, 0, 0, 0, 0, 0, 0, 16, 33, 186
"/Users/<USER>/development/flutter_apps/oneday/test/profile_edit_navigation_test.dart", "Dart", 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 17, 23, 99
"/Users/<USER>/development/flutter_apps/oneday/test/providers_integration_test.dart", "Dart", 0, 0, 0, 0, 83, 0, 0, 0, 0, 0, 0, 0, 9, 18, 110
"/Users/<USER>/development/flutter_apps/oneday/test/reflection_calendar_responsive_test.dart", "Dart", 0, 0, 0, 0, 140, 0, 0, 0, 0, 0, 0, 0, 30, 49, 219
"/Users/<USER>/development/flutter_apps/oneday/test/reflection_log_integration_test.dart", "Dart", 0, 0, 0, 0, 61, 0, 0, 0, 0, 0, 0, 0, 11, 19, 91
"/Users/<USER>/development/flutter_apps/oneday/test/responsive_layout_test.dart", "Dart", 0, 0, 0, 0, 230, 0, 0, 0, 0, 0, 0, 0, 12, 24, 266
"/Users/<USER>/development/flutter_apps/oneday/test/services/study_session_completion_service_test.dart", "Dart", 0, 0, 0, 0, 240, 0, 0, 0, 0, 0, 0, 0, 32, 39, 311
"/Users/<USER>/development/flutter_apps/oneday/test/services/study_session_completion_service_test.mocks.dart", "Dart", 0, 0, 0, 0, 402, 0, 0, 0, 0, 0, 0, 0, 26, 59, 487
"/Users/<USER>/development/flutter_apps/oneday/test/simple_profanity_test.dart", "Dart", 0, 0, 0, 0, 47, 0, 0, 0, 0, 0, 0, 0, 1, 16, 64
"/Users/<USER>/development/flutter_apps/oneday/test/standardized_coordinate_system_test.dart", "Dart", 0, 0, 0, 0, 269, 0, 0, 0, 0, 0, 0, 0, 28, 50, 347
"/Users/<USER>/development/flutter_apps/oneday/test/store_activation_test.dart", "Dart", 0, 0, 0, 0, 95, 0, 0, 0, 0, 0, 0, 0, 14, 23, 132
"/Users/<USER>/development/flutter_apps/oneday/test/store_new_items_test.dart", "Dart", 0, 0, 0, 0, 95, 0, 0, 0, 0, 0, 0, 0, 13, 27, 135
"/Users/<USER>/development/flutter_apps/oneday/test/task_category_management_page_test.dart", "Dart", 0, 0, 0, 0, 120, 0, 0, 0, 0, 0, 0, 0, 25, 39, 184
"/Users/<USER>/development/flutter_apps/oneday/test/task_category_test.dart", "Dart", 0, 0, 0, 0, 201, 0, 0, 0, 0, 0, 0, 0, 17, 48, 266
"/Users/<USER>/development/flutter_apps/oneday/test/task_sync_test.dart", "Dart", 0, 0, 0, 0, 164, 0, 0, 0, 0, 0, 0, 0, 27, 42, 233
"/Users/<USER>/development/flutter_apps/oneday/test/test_bubble_alignment_adjustment.dart", "Dart", 0, 0, 0, 0, 104, 0, 0, 0, 0, 0, 0, 0, 9, 23, 136
"/Users/<USER>/development/flutter_apps/oneday/test/test_bubble_alignment_verification.dart", "Dart", 0, 0, 0, 0, 104, 0, 0, 0, 0, 0, 0, 0, 10, 26, 140
"/Users/<USER>/development/flutter_apps/oneday/test/test_bubble_center_alignment.dart", "Dart", 0, 0, 0, 0, 93, 0, 0, 0, 0, 0, 0, 0, 8, 20, 121
"/Users/<USER>/development/flutter_apps/oneday/test/test_bubble_positioning_fix.dart", "Dart", 0, 0, 0, 0, 109, 0, 0, 0, 0, 0, 0, 0, 6, 27, 142
"/Users/<USER>/development/flutter_apps/oneday/test/test_compilation_fix.dart", "Dart", 0, 0, 0, 0, 144, 0, 0, 0, 0, 0, 0, 0, 7, 29, 180
"/Users/<USER>/development/flutter_apps/oneday/test/test_complete_image_compression.dart", "Dart", 0, 0, 0, 0, 186, 0, 0, 0, 0, 0, 0, 0, 9, 38, 233
"/Users/<USER>/development/flutter_apps/oneday/test/test_coordinate_analysis.dart", "Dart", 0, 0, 0, 0, 104, 0, 0, 0, 0, 0, 0, 0, 9, 27, 140
"/Users/<USER>/development/flutter_apps/oneday/test/test_coordinate_debug.dart", "Dart", 0, 0, 0, 0, 84, 0, 0, 0, 0, 0, 0, 0, 5, 18, 107
"/Users/<USER>/development/flutter_apps/oneday/test/test_coordinate_transformation_fix.dart", "Dart", 0, 0, 0, 0, 111, 0, 0, 0, 0, 0, 0, 0, 16, 24, 151
"/Users/<USER>/development/flutter_apps/oneday/test/test_image_compression_implementation.dart", "Dart", 0, 0, 0, 0, 147, 0, 0, 0, 0, 0, 0, 0, 7, 33, 187
"/Users/<USER>/development/flutter_apps/oneday/test/test_matrix_analysis.dart", "Dart", 0, 0, 0, 0, 130, 0, 0, 0, 0, 0, 0, 0, 8, 37, 175
"/Users/<USER>/development/flutter_apps/oneday/test/test_matrix_fix_verification.dart", "Dart", 0, 0, 0, 0, 105, 0, 0, 0, 0, 0, 0, 0, 14, 31, 150
"/Users/<USER>/development/flutter_apps/oneday/test/test_position_fix_verification.dart", "Dart", 0, 0, 0, 0, 38, 0, 0, 0, 0, 0, 0, 0, 4, 11, 53
"/Users/<USER>/development/flutter_apps/oneday/test/three_dot_menu_test.dart", "Dart", 0, 0, 0, 0, 108, 0, 0, 0, 0, 0, 0, 0, 24, 32, 164
"/Users/<USER>/development/flutter_apps/oneday/test/timebox_consistency_test.dart", "Dart", 0, 0, 0, 0, 137, 0, 0, 0, 0, 0, 0, 0, 26, 46, 209
"/Users/<USER>/development/flutter_apps/oneday/test/timebox_rest_skip_test.dart", "Dart", 0, 0, 0, 0, 192, 0, 0, 0, 0, 0, 0, 0, 20, 39, 251
"/Users/<USER>/development/flutter_apps/oneday/test/ui_overflow_test.dart", "Dart", 0, 0, 0, 0, 101, 0, 0, 0, 0, 0, 0, 0, 20, 32, 153
"/Users/<USER>/development/flutter_apps/oneday/test/vocabulary_scrollbar_test.dart", "Dart", 0, 0, 0, 0, 98, 0, 0, 0, 0, 0, 0, 0, 8, 22, 128
"/Users/<USER>/development/flutter_apps/oneday/test/vocabulary_test.dart", "Dart", 0, 0, 0, 0, 211, 0, 0, 0, 0, 0, 0, 0, 9, 28, 248
"/Users/<USER>/development/flutter_apps/oneday/test/widget_test.dart", "Dart", 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 9, 6, 26
"/Users/<USER>/development/flutter_apps/oneday/test/word_meaning_service_test.dart", "Dart", 0, 0, 0, 0, 61, 0, 0, 0, 0, 0, 0, 0, 2, 14, 77
"/Users/<USER>/development/flutter_apps/oneday/test_apps/category_cleanup_demo.dart", "Dart", 0, 0, 0, 0, 253, 0, 0, 0, 0, 0, 0, 0, 8, 26, 287
"/Users/<USER>/development/flutter_apps/oneday/test_apps/category_edit_demo.dart", "Dart", 0, 0, 0, 0, 276, 0, 0, 0, 0, 0, 0, 0, 7, 24, 307
"/Users/<USER>/development/flutter_apps/oneday/test_apps/category_management_demo.dart", "Dart", 0, 0, 0, 0, 100, 0, 0, 0, 0, 0, 0, 0, 1, 7, 108
"/Users/<USER>/development/flutter_apps/oneday/test_apps/category_removal_demo.dart", "Dart", 0, 0, 0, 0, 296, 0, 0, 0, 0, 0, 0, 0, 8, 26, 330
"/Users/<USER>/development/flutter_apps/oneday/test_apps/community_share_test.dart", "Dart", 0, 0, 0, 0, 317, 0, 0, 0, 0, 0, 0, 0, 19, 41, 377
"/Users/<USER>/development/flutter_apps/oneday/test_apps/pdf_color_demo.dart", "Dart", 0, 0, 0, 0, 256, 0, 0, 0, 0, 0, 0, 0, 14, 17, 287
"/Users/<USER>/development/flutter_apps/oneday/test_apps/premium_article_access_test.dart", "Dart", 0, 0, 0, 0, 107, 0, 0, 0, 0, 0, 0, 0, 22, 31, 160
"/Users/<USER>/development/flutter_apps/oneday/test_apps/simple_cleanup_test.dart", "Dart", 0, 0, 0, 0, 211, 0, 0, 0, 0, 0, 0, 0, 4, 23, 238
"/Users/<USER>/development/flutter_apps/oneday/test_apps/simple_dialog_test.dart", "Dart", 0, 0, 0, 0, 215, 0, 0, 0, 0, 0, 0, 0, 3, 17, 235
"/Users/<USER>/development/flutter_apps/oneday/test_apps/snackbar_duration_test.dart", "Dart", 0, 0, 0, 0, 172, 0, 0, 0, 0, 0, 0, 0, 6, 10, 188
"/Users/<USER>/development/flutter_apps/oneday/test_apps/system_category_share_demo.dart", "Dart", 0, 0, 0, 0, 296, 0, 0, 0, 0, 0, 0, 0, 12, 23, 331
"/Users/<USER>/development/flutter_apps/oneday/test_apps/task_count_verification.dart", "Dart", 0, 0, 0, 0, 330, 0, 0, 0, 0, 0, 0, 0, 13, 28, 371
"/Users/<USER>/development/flutter_apps/oneday/test_apps/task_sync_demo.dart", "Dart", 0, 0, 0, 0, 256, 0, 0, 0, 0, 0, 0, 0, 4, 27, 287
"/Users/<USER>/development/flutter_apps/oneday/test_apps/task_sync_verification.dart", "Dart", 0, 0, 0, 0, 320, 0, 0, 0, 0, 0, 0, 0, 9, 28, 357
"/Users/<USER>/development/flutter_apps/oneday/test_apps/wage_calculation_test.dart", "Dart", 0, 0, 0, 0, 228, 0, 0, 0, 0, 0, 0, 0, 9, 26, 263
"/Users/<USER>/development/flutter_apps/oneday/web/index.html", "HTML", 0, 0, 0, 0, 0, 0, 0, 19, 0, 0, 0, 0, 15, 5, 39
"/Users/<USER>/development/flutter_apps/oneday/web/manifest.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 35, 0, 0, 0, 0, 0, 35
"/Users/<USER>/development/flutter_apps/oneday/windows/CMakeLists.txt", "CMake", 0, 89, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 109
"/Users/<USER>/development/flutter_apps/oneday/windows/flutter/CMakeLists.txt", "CMake", 0, 98, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 110
"/Users/<USER>/development/flutter_apps/oneday/windows/flutter/generated_plugin_registrant.cc", "C++", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 5, 24
"/Users/<USER>/development/flutter_apps/oneday/windows/flutter/generated_plugin_registrant.h", "C++", 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 6, 16
"/Users/<USER>/development/flutter_apps/oneday/windows/flutter/generated_plugins.cmake", "CMake", 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/CMakeLists.txt", "CMake", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 41
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/flutter_window.cpp", "C++", 0, 0, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 16, 72
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/flutter_window.h", "C++", 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 9, 34
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/main.cpp", "C++", 0, 0, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 10, 44
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/resource.h", "C++", 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 2, 17
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/utils.cpp", "C++", 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 10, 66
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/utils.h", "C++", 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 6, 20
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/win32_window.cpp", "C++", 0, 0, 210, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 55, 289
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/win32_window.h", "C++", 0, 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 24, 103
"Total", "-", 26336, 467, 560, 97, 104995, 152, 64, 712, 85362, 141, 482, 8, 10272, 21026, 250674