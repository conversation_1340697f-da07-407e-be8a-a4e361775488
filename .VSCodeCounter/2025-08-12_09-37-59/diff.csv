"filename", "language", "Markdown", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "comment", "blank", "total"
"/Users/<USER>/development/flutter_apps/oneday/EMERGENCY_FIX_TEST_GUIDE.md", "Markdown", 65, 0, 0, 0, 0, 20, 85
"/Users/<USER>/development/flutter_apps/oneday/FORCE_RESTART_SCRIPT.md", "Markdown", 25, 0, 0, 0, 0, 7, 32
"/Users/<USER>/development/flutter_apps/oneday/IMAGE_GHOSTING_FIX_SUMMARY.md", "Markdown", 115, 0, 0, 0, 0, 32, 147
"/Users/<USER>/development/flutter_apps/oneday/PHOTO_ALBUM_CREATOR_FIXES.md", "Markdown", 82, 0, 0, 0, 0, 21, 103
"/Users/<USER>/development/flutter_apps/oneday/PHOTO_POSITION_FIX_SUMMARY.md", "Markdown", 109, 0, 0, 0, 0, 34, 143
"/Users/<USER>/development/flutter_apps/oneday/PHOTO_SELECTION_DEBUG_GUIDE.md", "Markdown", 95, 0, 0, 0, 0, 31, 126
"/Users/<USER>/development/flutter_apps/oneday/PHOTO_SELECTION_FIX_COMPLETE.md", "Markdown", 161, 0, 0, 0, 0, 41, 202
"/Users/<USER>/development/flutter_apps/oneday/PHOTO_SELECTION_FIX_TEST_GUIDE.md", "Markdown", 124, 0, 0, 0, 0, 35, 159
"/Users/<USER>/development/flutter_apps/oneday/PHOTO_SELECTION_FIX_VERIFICATION.md", "Markdown", 94, 0, 0, 0, 0, 24, 118
"/Users/<USER>/development/flutter_apps/oneday/PHOTO_SELECTION_LAYER_CONFLICT_FIX_GUIDE.md", "Markdown", 95, 0, 0, 0, 0, 26, 121
"/Users/<USER>/development/flutter_apps/oneday/PHOTO_SELECTION_ORDER_REFACTOR_COMPLETE.md", "Markdown", 115, 0, 0, 0, 0, 34, 149
"/Users/<USER>/development/flutter_apps/oneday/docs/PHOTO_SELECTION_TEST_GUIDE.md", "Markdown", 94, 0, 0, 0, 0, 26, 120
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/IOS_PHOTO_SELECTION_COMPREHENSIVE_SOLUTION.md", "Markdown", 204, 0, 0, 0, 0, 50, 254
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/IOS_SIMULATOR_PHOTO_SELECTION_FIX.md", "Markdown", 183, 0, 0, 0, 0, 65, 248
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/photo_album_dialog_position_fix.md", "Markdown", 100, 0, 0, 0, 0, 31, 131
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/IOS_SIMULATOR_PHOTO_FIX_TEST_GUIDE.md", "Markdown", 158, 0, 0, 0, 0, 50, 208
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/IOS_SIMULATOR_PHOTO_TESTING_GUIDE.md", "Markdown", 144, 0, 0, 0, 0, 39, 183
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/PHOTO_SELECTION_CANNOT_CONFIRM_ISSUE.md", "Markdown", 179, 0, 0, 0, 0, 47, 226
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/dialog_safe_area_test_page.dart", "Dart", 0, 356, 0, 0, 12, 21, 389
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/image_picker_debug_page.dart", "Dart", 0, 33, 0, 0, 2, 4, 39
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/ios_photo_selection_debugger.dart", "Dart", 0, 341, 0, 0, 28, 50, 419
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/ios_simulator_test_page.dart", "Dart", 0, 467, 0, 0, 25, 47, 539
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/photo_rendering_debugger.dart", "Dart", 0, 366, 0, 0, 26, 48, 440
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/photo_selection_advanced_diagnostics.dart", "Dart", 0, 439, 0, 0, 48, 74, 561
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/photo_selection_validator.dart", "Dart", 0, 168, 0, 0, 18, 35, 221
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/route_debug_page.dart", "Dart", 0, 15, 0, 0, 0, 0, 15
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/simple_image_picker_test.dart", "Dart", 0, 209, 0, 0, 11, 27, 247
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/simple_photo_test.dart", "Dart", 0, 137, 0, 0, 7, 16, 160
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/simulator_reset_helper.dart", "Dart", 0, 407, 0, 0, 25, 40, 472
"/Users/<USER>/development/flutter_apps/oneday/lib/features/auth/phone_login_page.dart", "Dart", 0, -43, 0, 0, 3, -5, -45
"/Users/<USER>/development/flutter_apps/oneday/lib/features/auth/providers/phone_login_provider.dart", "Dart", 0, -34, 0, 0, -5, -6, -45
"/Users/<USER>/development/flutter_apps/oneday/lib/features/auth/register_page.dart", "Dart", 0, -7, 0, 0, -1, -1, -9
"/Users/<USER>/development/flutter_apps/oneday/lib/features/home/<USER>", "Dart", 0, 17, 0, 0, 1, 2, 20
"/Users/<USER>/development/flutter_apps/oneday/lib/features/main/main_container_page.dart", "Dart", 0, 0, 0, 0, 1, 1, 2
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/palace_manager_page.dart", "Dart", 0, 53, 0, 0, 9, 8, 70
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page.dart", "Dart", 0, 42, 0, 0, 9, 5, 56
"/Users/<USER>/development/flutter_apps/oneday/lib/features/photo_album/photo_album_creator_page.dart", "Dart", 0, 849, 0, 0, 67, 74, 990
"/Users/<USER>/development/flutter_apps/oneday/lib/features/profile/pages/profile_edit_page.dart", "Dart", 0, 50, 0, 0, 7, 5, 62
"/Users/<USER>/development/flutter_apps/oneday/lib/features/settings/settings_page.dart", "Dart", 0, 17, 0, 0, 1, 1, 19
"/Users/<USER>/development/flutter_apps/oneday/lib/main.dart", "Dart", 0, 4, 0, 0, 0, 0, 4
"/Users/<USER>/development/flutter_apps/oneday/lib/router/app_router.dart", "Dart", 0, 35, 0, 0, 5, 7, 47
"/Users/<USER>/development/flutter_apps/oneday/lib/utils/image_rendering_fix.dart", "Dart", 0, 135, 0, 0, 10, 10, 155
"/Users/<USER>/development/flutter_apps/oneday/lib/utils/ios_simulator_detector.dart", "Dart", 0, 157, 0, 0, 20, 30, 207
"/Users/<USER>/development/flutter_apps/oneday/lib/utils/safe_area_helper.dart", "Dart", 0, 475, 0, 0, 66, 65, 606
"/Users/<USER>/development/flutter_apps/oneday/macos/Flutter/GeneratedPluginRegistrant.swift", "Swift", 0, 0, 4, 0, 0, 0, 4
"/Users/<USER>/development/flutter_apps/oneday/pubspec.yaml", "YAML", 0, 0, 0, 2, 2, 2, 6
"/Users/<USER>/development/flutter_apps/oneday/tempPrompts.md", "Markdown", 19, 0, 0, 0, 0, 6, 25
"/Users/<USER>/development/flutter_apps/oneday/test/photo_album_position_fix_test.dart", "Dart", 0, 32, 0, 0, 7, 11, 50
"/Users/<USER>/development/flutter_apps/oneday/test/photo_selection_order_test.dart", "Dart", 0, 78, 0, 0, 14, 21, 113
"/Users/<USER>/development/flutter_apps/oneday/test/photo_selection_unit_test.dart", "Dart", 0, 66, 0, 0, 8, 12, 86
"Total", "-", 2161, 4864, 4, 2, 426, 1223, 8680